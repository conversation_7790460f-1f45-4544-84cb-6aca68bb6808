#!/bin/bash

set -e

# 配置变量
TRAINJOB_NAME="llm-sft-with-pvc"
NAMESPACE="default"
STORAGE_CLASS="nfs-storage"  # 根据您的集群配置调整
PVC_SIZE="500Gi"

echo "=== 部署 SFT 训练任务 (使用 PVC) ==="

# 1. 部署 ClusterTrainingRuntime
echo "1. 部署 ClusterTrainingRuntime..."
kubectl apply -f sft-with-pvc-runtime.yaml

# 2. 创建 PVC
echo "2. 创建 PVC: ${TRAINJOB_NAME}-workspace..."
cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ${TRAINJOB_NAME}-workspace
  namespace: ${NAMESPACE}
  labels:
    app: trainer
    trainjob: ${TRAINJOB_NAME}
    component: shared-workspace
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: ${PVC_SIZE}
  storageClassName: ${STORAGE_CLASS}
EOF

# 3. 创建 TrainJob
echo "3. 创建 TrainJob..."
cat <<EOF | kubectl apply -f -
apiVersion: trainer.kubeflow.org/v1alpha1
kind: TrainJob
metadata:
  name: ${TRAINJOB_NAME}
  namespace: ${NAMESPACE}
spec:
  runtimeRef:
    name: sft-with-pvc
    apiGroup: trainer.kubeflow.org
    kind: ClusterTrainingRuntime
  
  initializer:
    dataset:
      storageUri: "cc://root/alpaca-gpt4-data-zh"
      secretRef: 
        name: cetccloud-credentials
      env:
        - name: DOWNLOAD_PATH
          value: "/workspace/data"
    model:
      storageUri: "cc://root/Qwen2.5-7B-Instruct"
      secretRef:
        name: cetccloud-credentials
      env:
        - name: DOWNLOAD_PATH
          value: "/workspace/model"
  
  trainer:
    image: 172.28.0.32:3443/jdcloud/swift:ubuntu22.04-cuda12.4.0-py311-torch2.6.0-vllm0.8.5.post1-modelscope1.26.0-swift3.4.1.post1
    command: ["swift"]
    args:
      - sft
      - --model_type=qwen2_5-7b-instruct
      - --model_id_or_path=/workspace/model
      - --dataset_dir=/workspace/data
      - --output_dir=/workspace/output
      - --num_train_epochs=1
      - --per_device_train_batch_size=1
      - --learning_rate=1e-4
      - --lora_rank=8
      - --lora_alpha=32
    numNodes: 2
    numProcPerNode: 2
    resourcesPerNode:
      requests:
        nvidia.com/gpu: 2
        cpu: 8
        memory: 32Gi
  
  podSpecOverrides:
    - targetJobs:
        - name: dataset-initializer
      volumes:
        - name: shared-workspace
          persistentVolumeClaim:
            claimName: ${TRAINJOB_NAME}-workspace
    - targetJobs:
        - name: model-initializer
      volumes:
        - name: shared-workspace
          persistentVolumeClaim:
            claimName: ${TRAINJOB_NAME}-workspace
    - targetJobs:
        - name: node
      volumes:
        - name: shared-workspace
          persistentVolumeClaim:
            claimName: ${TRAINJOB_NAME}-workspace
  
  labels:
    experiment: "llama-sft-pvc"
    version: "v1.0"
EOF

echo "=== 部署完成 ==="
echo ""
echo "检查状态:"
echo "kubectl get trainjobs ${TRAINJOB_NAME} -n ${NAMESPACE}"
echo "kubectl get pvc ${TRAINJOB_NAME}-workspace -n ${NAMESPACE}"
echo "kubectl get jobsets -n ${NAMESPACE}"
echo "kubectl get pods -n ${NAMESPACE} -l trainer.kubeflow.org/trainjob-name=${TRAINJOB_NAME}"
echo ""
echo "查看日志:"
echo "kubectl logs -n ${NAMESPACE} -l trainer.kubeflow.org/trainjob-name=${TRAINJOB_NAME} -c dataset-initializer"
echo "kubectl logs -n ${NAMESPACE} -l trainer.kubeflow.org/trainjob-name=${TRAINJOB_NAME} -c model-initializer"
echo "kubectl logs -n ${NAMESPACE} -l trainer.kubeflow.org/trainjob-name=${TRAINJOB_NAME} -c node"
