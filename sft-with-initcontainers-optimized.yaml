apiVersion: trainer.kubeflow.org/v1alpha1
kind: ClusterTrainingRuntime
metadata:
  name: sft-with-initcontainers-optimized
spec:
  mlPolicy:
    numNodes: 1
    torch:
      numProcPerNode: auto
  template:
    spec:
      replicatedJobs:
        # 训练 Job - 包含 initContainers
        - name: node
          template:
            metadata:
              labels:
                trainer.kubeflow.org/trainjob-ancestor-step: trainer
            spec:
              template:
                spec:
                  # Pod 重启策略
                  restartPolicy: Never
                  
                  # Init Containers - 按顺序执行数据集和模型初始化
                  initContainers:
                    # 数据集初始化器 InitContainer
                    - name: dataset-initializer
                      image: hub.cetccloud.io:5000/jdcloud/kserve-storage-initializer:v2.0.0
                      env:
                        - name: STORAGE_URI
                          value: ""  # 将由 TrainJob 覆盖
                        - name: CETCCLOUD_REPO_TYPE
                          value: dataset
                      volumeMounts:
                        - name: shared-workspace
                          mountPath: /data  # 数据集下载到 /data，映射到共享卷的 data 目录
                      command:
                        - /bin/sh
                        - -c
                      args:
                        - |
                          echo "Starting dataset initialization..."
                          # 这里会被 TrainJob controller 添加 --storage-uri 参数
                          echo "Dataset initialization completed"
                    
                    # 模型初始化器 InitContainer  
                    - name: model-initializer
                      image: hub.cetccloud.io:5000/jdcloud/kserve-storage-initializer:v2.0.0
                      env:
                        - name: STORAGE_URI
                          value: ""  # 将由 TrainJob 覆盖
                      volumeMounts:
                        - name: shared-workspace
                          mountPath: /model  # 模型下载到 /model，映射到共享卷的 model 目录
                      command:
                        - /bin/sh
                        - -c
                      args:
                        - |
                          echo "Starting model initialization..."
                          # 这里会被 TrainJob controller 添加 --storage-uri 参数
                          echo "Model initialization completed"
                  
                  # 主训练容器
                  containers:
                    - name: node
                      image: pytorch/pytorch:2.7.1-cuda12.8-cudnn9-runtime
                      volumeMounts:
                        - name: shared-workspace
                          mountPath: /workspace
                      env:
                        - name: MODEL_PATH
                          value: "/workspace/model"
                        - name: DATA_PATH
                          value: "/workspace/data"
                        - name: OUTPUT_PATH
                          value: "/workspace/output"
                      command:
                        - /bin/bash
                        - -c
                      args:
                        - |
                          echo "Starting training..."
                          echo "Model path: $MODEL_PATH"
                          echo "Data path: $DATA_PATH"
                          echo "Output path: $OUTPUT_PATH"
                          # 训练命令将由 TrainJob 的 trainer.command 和 trainer.args 覆盖
                  
                  # 共享的 emptyDir 卷
                  volumes:
                    - name: shared-workspace
                      emptyDir:
                        sizeLimit: 200Gi  # 为模型、数据集和输出预留足够空间
