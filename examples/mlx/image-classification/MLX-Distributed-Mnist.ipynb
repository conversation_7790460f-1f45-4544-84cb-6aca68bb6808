{"cells": [{"cell_type": "markdown", "id": "629c902e-6cd0-4475-b6ce-5d6e37d7e2f3", "metadata": {}, "source": ["# Distributed MNIST with MLX and Kubeflow Trainer\n", "\n", "This Notebook will show how to run distributed MLX on Kubernetes with Kubeflow Trainer.\n", "\n", "We will use the MLX Runtime to created distributed training using OpenMPI and local minikube cluster.\n", "\n", "MLX Distributed: https://ml-explore.github.io/mlx/build/html/usage/distributed.html\n", "\n", "Minikube: https://minikube.sigs.k8s.io/docs/\n", "\n"]}, {"cell_type": "markdown", "id": "34b60ad6-36d6-430f-992e-51440b0bed8f", "metadata": {}, "source": ["## (Optional) Create minikube cluster with shared volume\n", "\n", "This notebook exports the trained model to your local volume for evaluation.\n", "\n", "To do this, follow these steps:\n", "\n", "Create a Minikube cluster with a mounted path. The `mlx-model` folder needs to be accessible by the `mpiuser`:\n", "\n", "```sh\n", "mkdir mlx-model\n", "chmod 777 mlx-model\n", "minikube start --cpus=8 --mount --mount-string=\"$(pwd):/mnt/data\"\n", "```\n", "\n", "After that you can patch the ClusterTrainingRuntime using the following command:\n", "\n", "```yaml\n", "kubectl patch clustertrainingruntime mlx-distributed --type='json' -p='[\n", "  {\n", "    \"op\": \"add\",\n", "    \"path\": \"/spec/template/spec/replicatedJobs/0/template/spec/template/spec/containers/0/volumeMounts\",\n", "    \"value\": [\n", "      {\n", "        \"name\": \"mlx-model\",\n", "        \"mountPath\": \"/home/<USER>/mlx-model\"\n", "      }\n", "    ]\n", "  },\n", "  {\n", "    \"op\": \"add\",\n", "    \"path\": \"/spec/template/spec/replicatedJobs/0/template/spec/template/spec/volumes\",\n", "    \"value\": [\n", "      {\n", "        \"name\": \"mlx-model\",\n", "        \"hostPath\": {\n", "          \"path\": \"/mnt/data/mlx-model\"\n", "        }\n", "      }\n", "    ]\n", "  }\n", "]'\n", "```"]}, {"metadata": {}, "cell_type": "markdown", "source": ["## Install the Kubeflow SDK\n", "\n", "You need to install the Kubeflow SDK to interact with Kubeflow Trainer APIs:"], "id": "3cb9ab4c8e12221a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "# !pip install git+https://github.com/kubeflow/sdk.git@main#subdirectory=python", "id": "bd62189280760f42"}, {"cell_type": "markdown", "id": "b5ad0e01-d893-484c-8988-d25c2f322b80", "metadata": {}, "source": ["## Create MLX training script\n", "\n", "We need to wrap our training script into a function to create Kubeflow TrainJob.\n", "\n", "This is the simple MLP model to recognize digits from the MNIST dataset."]}, {"cell_type": "code", "execution_count": 1, "id": "57952ce1-5752-4976-8a35-c71d25935b74", "metadata": {}, "outputs": [], "source": ["def mlx_train_mnist(args):\n", "    import time\n", "    from functools import partial\n", "    import mlx.core as mx\n", "    import mlx.nn as nn\n", "    import mlx.optimizers as optim\n", "    from mlx.data.datasets import load_mnist\n", "\n", "    # Define a simple MLP model with MLX.\n", "    class MLP(nn.Mo<PERSON>le):\n", "        def __init__(\n", "            self, in_dims: int, hidden_dims: int, num_layers: int, out_dims: int\n", "        ):\n", "            super().__init__()\n", "            layer_sizes = [in_dims] + [hidden_dims] * num_layers + [out_dims]\n", "            self.layers = [\n", "                nn.Linear(idim, odim)\n", "                for idim, odim in zip(layer_sizes[:-1], layer_sizes[1:])\n", "            ]\n", "\n", "        def __call__(self, x):\n", "            for layer in self.layers[:-1]:\n", "                x = nn.relu(layer(x))\n", "            return self.layers[-1](x)\n", "\n", "    # Try to initialize MLX distributed, otherwise run code in non-distributed.\n", "    try:\n", "        dist = mx.distributed.init(strict=True, backend=\"mpi\")\n", "        world_size = dist.size()\n", "        rank = dist.rank()\n", "        print(f\"Start Distributed Training, WORLD_SIZE: {world_size}, RANK: {rank}\")\n", "    except Exception:\n", "        world_size = 1\n", "        rank = 0\n", "        print(\"Start non-Distributed Training\")\n", "\n", "    # Load MNIST dataset and partition it.\n", "    BATCH_SIZE = 128\n", "    train_dataset = load_mnist()\n", "\n", "    distributed_ds = (\n", "        train_dataset.shuffle()\n", "        .partition_if(world_size > 1, world_size, rank)\n", "        .key_transform(\"image\", lambda x: (x.astype(\"float32\") / 255.0).ravel())\n", "    )\n", "\n", "\n", "    # Create the MLP model and SGD optimizer\n", "    model = MLP(\n", "        in_dims=distributed_ds[0][\"image\"].shape[-1],\n", "        hidden_dims=32,\n", "        num_layers=2,\n", "        out_dims=10,\n", "    )\n", "    optimizer = optim.SGD(learning_rate=0.01)\n", "\n", "    # Define function to calculate loss and accuracy.\n", "    def loss_fn(model, x, y):\n", "        output = model(x)\n", "        loss = mx.mean(nn.losses.cross_entropy(output, y))\n", "        acc = mx.mean(mx.argmax(output, axis=1) == y)\n", "        return loss, acc\n", "\n", "    # Define single training step.\n", "    @partial(mx.compile, inputs=model.state, outputs=model.state)\n", "    def step(x, y):\n", "        loss_and_grad_fn = nn.value_and_grad(model, loss_fn)\n", "        (loss, acc), grads = loss_and_grad_fn(model, x, y)\n", "        # Average grads to aggregate them across distributed nodes.\n", "        grads = nn.utils.average_gradients(grads)\n", "        optimizer.update(model, grads)\n", "        return loss, acc\n", "\n", "    # Average statistic across distributed nodes.\n", "    def average_stats(stats, count):\n", "        with mx.stream(mx.cpu):\n", "            stats = mx.distributed.all_sum(mx.array(stats))\n", "            count = mx.distributed.all_sum(count)\n", "            return (stats / count).tolist()\n", "\n", "    # Start distributed training.\n", "    for epoch in range(10):\n", "        epoch_start = time.perf_counter()\n", "        losses = accuracies = count = 0\n", "\n", "        for batch_idx, batch_sample in enumerate(distributed_ds.batch(BATCH_SIZE)):\n", "            x = mx.array(batch_sample[\"image\"])\n", "            y = mx.array(batch_sample[\"label\"])\n", "            loss, acc = step(x, y)\n", "            mx.eval(loss, acc, model.state)\n", "\n", "            losses += loss.item()\n", "            accuracies += acc.item()\n", "            count += 1\n", "\n", "            # Print the results.\n", "            if batch_idx % 10 == 0:\n", "                loss, acc = average_stats([losses, accuracies],count)\n", "                if rank == 0:\n", "                    print(\n", "                        \"Epoch: {} [{}/{} ({:.0f}%)] \\tTrain loss: {:.3f}, acc: {:.3f}\".format(\n", "                            epoch,\n", "                            batch_idx * len(x),\n", "                            len(train_dataset),\n", "                            100.0 * batch_idx * len(x) / len(train_dataset),\n", "                            loss,\n", "                            acc,\n", "                        )\n", "                    )\n", "        if rank == 0:\n", "            print(\n", "                \"Epoch: {}, time: {:.2f} seconds\\n\\n\".format(\n", "                    epoch, time.perf_counter() - epoch_start\n", "                )\n", "            )\n", "    if rank == 0: \n", "        # Finally, save the trained model to the disk.    \n", "        model.save_weights(args[\"MODEL_PATH\"])"]}, {"cell_type": "markdown", "id": "875abc44-69dd-41d8-bb2a-436f730a2dd0", "metadata": {}, "source": ["## List Available Kubeflow Trainer Runtimes\n", "\n", "\n", "Get available Kubeflow Trainer Runtimes with the `list_runtimes()` API.\n", "\n", "You can inspect Runtime details, including the name, framework, entry point, and number of accelerators.\n", "\n", "- Runtimes with **CustomTrainer**: You must write the training script within the function.\n", "\n", "- Runtimes with **BuiltinTrainer**: You can configure settings (e.g., LoRA Config) for LLM fine-tuning Job.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "51d8bc1d-8d9b-48f7-866f-c6ad4ad4241b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: mlx-distributed, Framework: mlx, Trainer Type: CustomTrainer\n", "\n", "Entrypoint: ['mpirun', '--hostfile', '/etc/mpi/hostfile']\n", "\n"]}], "source": ["from kubeflow.trainer import TrainerClient, CustomTrainer\n", "\n", "for r in TrainerClient().list_runtimes():\n", "    print(f\"Name: {r.name}, Framework: {r.trainer.framework.value}, Trainer Type: {r.trainer.trainer_type.value}\\n\")\n", "    print(f\"Entrypoint: {r.trainer.entrypoint[:3]}\\n\")\n", "\n", "    if r.name == \"mlx-distributed\":\n", "        mlx_runtime = r"]}, {"cell_type": "markdown", "id": "206c389d-9dcb-474f-b0f5-cc098de2e183", "metadata": {}, "source": ["## Create <PERSON><PERSON><PERSON> for Distributed Training\n", "\n", "Use the `train()` API to create distributed TrainJob on 2 MPI Nodes."]}, {"cell_type": "code", "execution_count": 3, "id": "76dab189-f184-4e48-be74-f32c0dea675b", "metadata": {}, "outputs": [], "source": ["# The `mlx-model` folder must be created.\n", "MODEL_PATH = \"mlx-model/model.npz\"\n", "args = {\n", "    \"MODEL_PATH\": MODEL_PATH\n", "}\n", "\n", "job_id = TrainerClient().train(\n", "    trainer=CustomTrainer(\n", "        func=mlx_train_mnist,\n", "        func_args=args,\n", "        num_nodes=3,\n", "    ),\n", "    runtime=mlx_runtime,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "6e1811dd-4bf2-40cf-ad35-35a87271eb21", "metadata": {}, "outputs": [{"data": {"text/plain": ["'y5e1863ee6de'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train API generates a random TrainJob id.\n", "job_id"]}, {"cell_type": "markdown", "id": "f0864121-895f-4e5a-87b8-7ea2d92e6630", "metadata": {}, "source": ["## Check the TrainJob Info\n", "\n", "Use the `list_jobs()` and `get_job()` APIs to get information about created TrainJob and its steps."]}, {"cell_type": "code", "execution_count": 5, "id": "8a945930-6cfe-4388-8ab8-462474f3f21f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TrainJob: b85d5f28b0ea, Status: Succeeded, Created at: 2025-03-26 17:10:13+00:00\n", "TrainJob: g02078f3e66f, Status: Succeeded, Created at: 2025-03-26 17:14:49+00:00\n", "TrainJob: y5e1863ee6de, Status: Created, Created at: 2025-03-26 17:18:18+00:00\n"]}], "source": ["for job in TrainerClient().list_jobs():\n", "    print(f\"TrainJob: {job.name}, Status: {job.status}, Created at: {job.creation_timestamp}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "75e8c741-6d31-49a2-8667-d38e40d62430", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step: node-0, Status: Running, Devices: cpu x 2\n", "\n", "Step: node-1, Status: Running, Devices: cpu x 2\n", "\n", "Step: node-2, Status: Running, Devices: cpu x 2\n", "\n"]}], "source": ["# We execute mpirun command on node-0, which functions as the MPI Launcher node.\n", "for c in TrainerClient().get_job(name=job_id).steps:\n", "    print(f\"Step: {c.name}, Status: {c.status}, Devices: {c.device} x {c.device_count}\\n\")"]}, {"cell_type": "markdown", "id": "d4fa3ea2-bdf5-40ca-a9ec-cc948949ea59", "metadata": {}, "source": ["## Get the TrainJob Logs\n", "\n", "Use the `get_job_logs()` API to retrieve the TrainJob logs.\n", "\n", "Since we distribute the dataset across 3 nodes, each rank processes `round(60,000 / 3) = 20,000` samples."]}, {"cell_type": "code", "execution_count": 9, "id": "6e630fd3-f061-4fea-8024-7bffcefb257c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[node-0]: Warning: Permanently added '[y5e1863ee6de-node-0-1.y5e1863ee6de]:2222' (ECDSA) to the list of known hosts.\n", "[node-0]: Warning: Permanently added '[y5e1863ee6de-node-0-0.y5e1863ee6de]:2222' (ECDSA) to the list of known hosts.\n", "[node-0]: Start Distributed Training, WORLD_SIZE: 3, RANK: 1\n", "[node-0]: Start Distributed Training, WORLD_SIZE: 3, RANK: 0\n", "[node-0]: Start Distributed Training, WORLD_SIZE: 3, RANK: 2\n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/train-images-idx3-ubyte.gz 9.5MiB (12.6MiB/s)                                                        \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/t10k-images-idx3-ubyte.gz 1.6MiB (10.1MiB/s)                                                         \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/train-images-idx3-ubyte.gz 9.5MiB (7.5MiB/s)                                                         \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/train-labels-idx1-ubyte.gz 32.0KiB (30.9MiB/s)                                                     \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/t10k-images-idx3-ubyte.gz 1.6MiB (13.7MiB/s)                                                       \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/train-labels-idx1-ubyte.gz 32.0KiB (18.5MiB/s)                                                     \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/t10k-labels-idx1-ubyte.gz 8.0KiB (47.4MiB/s)                                               iB/s)   \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/t10k-labels-idx1-ubyte.gz 8.0KiB (62.9MiB/s)                                               iB/s)   \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/train-images-idx3-ubyte.gz 9.5MiB (5.9MiB/s)                                                         \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/t10k-images-idx3-ubyte.gz 1.6MiB (18.6MiB/s)                                                       \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/train-labels-idx1-ubyte.gz 32.0KiB (4.5MiB/s)                                                     \n", "Downloading https://raw.githubusercontent.com/fgnt/mnist/master/t10k-labels-idx1-ubyte.gz 8.0KiB (1.8MiB/s)                                                \n", "[node-0]: Epoch: 0 [0/60000 (0%)] \tTrain loss: 2.301, acc: 0.115\n", "[node-0]: Epoch: 0 [1280/60000 (2%)] \tTrain loss: 2.306, acc: 0.100\n", "[node-0]: Epoch: 0 [2560/60000 (4%)] \tTrain loss: 2.307, acc: 0.096\n", "[node-0]: Epoch: 0 [3840/60000 (6%)] \tTrain loss: 2.306, acc: 0.098\n", "[node-0]: Epoch: 0 [5120/60000 (9%)] \tTrain loss: 2.306, acc: 0.097\n", "[node-0]: Epoch: 0 [6400/60000 (11%)] \tTrain loss: 2.305, acc: 0.098\n", "[node-0]: Epoch: 0 [7680/60000 (13%)] \tTrain loss: 2.305, acc: 0.097\n", "[node-0]: Epoch: 0 [8960/60000 (15%)] \tTrain loss: 2.305, acc: 0.098\n", "[node-0]: Epoch: 0 [10240/60000 (17%)] \tTrain loss: 2.304, acc: 0.099\n", "[node-0]: Epoch: 0 [11520/60000 (19%)] \tTrain loss: 2.303, acc: 0.099\n", "[node-0]: Epoch: 0 [12800/60000 (21%)] \tTrain loss: 2.303, acc: 0.100\n", "[node-0]: Epoch: 0 [14080/60000 (23%)] \tTrain loss: 2.302, acc: 0.100\n", "[node-0]: Epoch: 0 [15360/60000 (26%)] \tTrain loss: 2.302, acc: 0.100\n", "[node-0]: Epoch: 0 [16640/60000 (28%)] \tTrain loss: 2.301, acc: 0.100\n", "[node-0]: Epoch: 0 [17920/60000 (30%)] \tTrain loss: 2.300, acc: 0.101\n", "[node-0]: Epoch: 0 [19200/60000 (32%)] \tTrain loss: 2.300, acc: 0.102\n", "[node-0]: Epoch: 0, time: 16.08 seconds\n", "[node-0]: Epoch: 1 [0/60000 (0%)] \tTrain loss: 2.285, acc: 0.130\n", "[node-0]: Epoch: 1 [1280/60000 (2%)] \tTrain loss: 2.289, acc: 0.121\n", "[node-0]: Epoch: 1 [2560/60000 (4%)] \tTrain loss: 2.290, acc: 0.116\n", "[node-0]: Epoch: 1 [3840/60000 (6%)] \tTrain loss: 2.288, acc: 0.120\n", "[node-0]: Epoch: 1 [5120/60000 (9%)] \tTrain loss: 2.288, acc: 0.120\n", "[node-0]: Epoch: 1 [6400/60000 (11%)] \tTrain loss: 2.287, acc: 0.121\n", "[node-0]: Epoch: 1 [7680/60000 (13%)] \tTrain loss: 2.287, acc: 0.123\n", "[node-0]: Epoch: 1 [8960/60000 (15%)] \tTrain loss: 2.286, acc: 0.125\n", "[node-0]: Epoch: 1 [10240/60000 (17%)] \tTrain loss: 2.285, acc: 0.126\n", "[node-0]: Epoch: 1 [11520/60000 (19%)] \tTrain loss: 2.285, acc: 0.129\n", "[node-0]: Epoch: 1 [12800/60000 (21%)] \tTrain loss: 2.284, acc: 0.131\n", "[node-0]: Epoch: 1 [14080/60000 (23%)] \tTrain loss: 2.283, acc: 0.134\n", "[node-0]: Epoch: 1 [15360/60000 (26%)] \tTrain loss: 2.282, acc: 0.136\n", "[node-0]: Epoch: 1 [16640/60000 (28%)] \tTrain loss: 2.281, acc: 0.139\n", "[node-0]: Epoch: 1 [17920/60000 (30%)] \tTrain loss: 2.280, acc: 0.141\n", "[node-0]: Epoch: 1 [19200/60000 (32%)] \tTrain loss: 2.279, acc: 0.144\n", "[node-0]: Epoch: 1, time: 6.10 seconds\n", "[node-0]: Epoch: 2 [0/60000 (0%)] \tTrain loss: 2.261, acc: 0.195\n", "[node-0]: Epoch: 2 [1280/60000 (2%)] \tTrain loss: 2.263, acc: 0.189\n", "[node-0]: Epoch: 2 [2560/60000 (4%)] \tTrain loss: 2.264, acc: 0.185\n", "[node-0]: Epoch: 2 [3840/60000 (6%)] \tTrain loss: 2.262, acc: 0.190\n", "[node-0]: Epoch: 2 [5120/60000 (9%)] \tTrain loss: 2.261, acc: 0.190\n", "[node-0]: Epoch: 2 [6400/60000 (11%)] \tTrain loss: 2.260, acc: 0.190\n", "[node-0]: Epoch: 2 [7680/60000 (13%)] \tTrain loss: 2.259, acc: 0.192\n", "[node-0]: Epoch: 2 [8960/60000 (15%)] \tTrain loss: 2.258, acc: 0.193\n", "[node-0]: Epoch: 2 [10240/60000 (17%)] \tTrain loss: 2.257, acc: 0.194\n", "[node-0]: Epoch: 2 [11520/60000 (19%)] \tTrain loss: 2.256, acc: 0.196\n", "[node-0]: Epoch: 2 [12800/60000 (21%)] \tTrain loss: 2.255, acc: 0.197\n", "[node-0]: Epoch: 2 [14080/60000 (23%)] \tTrain loss: 2.253, acc: 0.199\n", "[node-0]: Epoch: 2 [15360/60000 (26%)] \tTrain loss: 2.252, acc: 0.200\n", "[node-0]: Epoch: 2 [16640/60000 (28%)] \tTrain loss: 2.251, acc: 0.202\n", "[node-0]: Epoch: 2 [17920/60000 (30%)] \tTrain loss: 2.249, acc: 0.204\n", "[node-0]: Epoch: 2 [19200/60000 (32%)] \tTrain loss: 2.248, acc: 0.206\n", "[node-0]: Epoch: 2, time: 6.07 seconds\n", "[node-0]: Epoch: 3 [0/60000 (0%)] \tTrain loss: 2.224, acc: 0.237\n", "[node-0]: Epoch: 3 [1280/60000 (2%)] \tTrain loss: 2.225, acc: 0.245\n", "[node-0]: Epoch: 3 [2560/60000 (4%)] \tTrain loss: 2.225, acc: 0.243\n", "[node-0]: Epoch: 3 [3840/60000 (6%)] \tTrain loss: 2.221, acc: 0.252\n", "[node-0]: Epoch: 3 [5120/60000 (9%)] \tTrain loss: 2.220, acc: 0.256\n", "[node-0]: Epoch: 3 [6400/60000 (11%)] \tTrain loss: 2.218, acc: 0.258\n", "[node-0]: Epoch: 3 [7680/60000 (13%)] \tTrain loss: 2.217, acc: 0.262\n", "[node-0]: Epoch: 3 [8960/60000 (15%)] \tTrain loss: 2.215, acc: 0.263\n", "[node-0]: Epoch: 3 [10240/60000 (17%)] \tTrain loss: 2.213, acc: 0.264\n", "[node-0]: Epoch: 3 [11520/60000 (19%)] \tTrain loss: 2.212, acc: 0.267\n", "[node-0]: Epoch: 3 [12800/60000 (21%)] \tTrain loss: 2.210, acc: 0.269\n", "[node-0]: Epoch: 3 [14080/60000 (23%)] \tTrain loss: 2.207, acc: 0.272\n", "[node-0]: Epoch: 3 [15360/60000 (26%)] \tTrain loss: 2.206, acc: 0.274\n", "[node-0]: Epoch: 3 [16640/60000 (28%)] \tTrain loss: 2.203, acc: 0.277\n", "[node-0]: Epoch: 3 [17920/60000 (30%)] \tTrain loss: 2.201, acc: 0.280\n", "[node-0]: Epoch: 3 [19200/60000 (32%)] \tTrain loss: 2.199, acc: 0.283\n", "[node-0]: Epoch: 3, time: 6.04 seconds\n", "[node-0]: Epoch: 4 [0/60000 (0%)] \tTrain loss: 2.163, acc: 0.326\n", "[node-0]: Epoch: 4 [1280/60000 (2%)] \tTrain loss: 2.161, acc: 0.329\n", "[node-0]: Epoch: 4 [2560/60000 (4%)] \tTrain loss: 2.160, acc: 0.333\n", "[node-0]: Epoch: 4 [3840/60000 (6%)] \tTrain loss: 2.155, acc: 0.340\n", "[node-0]: Epoch: 4 [5120/60000 (9%)] \tTrain loss: 2.152, acc: 0.345\n", "[node-0]: Epoch: 4 [6400/60000 (11%)] \tTrain loss: 2.149, acc: 0.344\n", "[node-0]: Epoch: 4 [7680/60000 (13%)] \tTrain loss: 2.146, acc: 0.345\n", "[node-0]: Epoch: 4 [8960/60000 (15%)] \tTrain loss: 2.143, acc: 0.345\n", "[node-0]: Epoch: 4 [10240/60000 (17%)] \tTrain loss: 2.140, acc: 0.344\n", "[node-0]: Epoch: 4 [11520/60000 (19%)] \tTrain loss: 2.137, acc: 0.345\n", "[node-0]: Epoch: 4 [12800/60000 (21%)] \tTrain loss: 2.134, acc: 0.345\n", "[node-0]: Epoch: 4 [14080/60000 (23%)] \tTrain loss: 2.130, acc: 0.346\n", "[node-0]: Epoch: 4 [15360/60000 (26%)] \tTrain loss: 2.127, acc: 0.346\n", "[node-0]: Epoch: 4 [16640/60000 (28%)] \tTrain loss: 2.124, acc: 0.346\n", "[node-0]: Epoch: 4 [17920/60000 (30%)] \tTrain loss: 2.120, acc: 0.347\n", "[node-0]: Epoch: 4 [19200/60000 (32%)] \tTrain loss: 2.116, acc: 0.348\n", "[node-0]: Epoch: 4, time: 5.87 seconds\n", "[node-0]: Epoch: 5 [0/60000 (0%)] \tTrain loss: 2.061, acc: 0.346\n", "[node-0]: Epoch: 5 [1280/60000 (2%)] \tTrain loss: 2.057, acc: 0.364\n", "[node-0]: Epoch: 5 [2560/60000 (4%)] \tTrain loss: 2.053, acc: 0.367\n", "[node-0]: Epoch: 5 [3840/60000 (6%)] \tTrain loss: 2.044, acc: 0.374\n", "[node-0]: Epoch: 5 [5120/60000 (9%)] \tTrain loss: 2.039, acc: 0.377\n", "[node-0]: Epoch: 5 [6400/60000 (11%)] \tTrain loss: 2.035, acc: 0.378\n", "[node-0]: Epoch: 5 [7680/60000 (13%)] \tTrain loss: 2.030, acc: 0.377\n", "[node-0]: Epoch: 5 [8960/60000 (15%)] \tTrain loss: 2.026, acc: 0.379\n", "[node-0]: Epoch: 5 [10240/60000 (17%)] \tTrain loss: 2.022, acc: 0.379\n", "[node-0]: Epoch: 5 [11520/60000 (19%)] \tTrain loss: 2.018, acc: 0.381\n", "[node-0]: Epoch: 5 [12800/60000 (21%)] \tTrain loss: 2.013, acc: 0.381\n", "[node-0]: Epoch: 5 [14080/60000 (23%)] \tTrain loss: 2.007, acc: 0.384\n", "[node-0]: Epoch: 5 [15360/60000 (26%)] \tTrain loss: 2.003, acc: 0.385\n", "[node-0]: Epoch: 5 [16640/60000 (28%)] \tTrain loss: 1.997, acc: 0.387\n", "[node-0]: Epoch: 5 [17920/60000 (30%)] \tTrain loss: 1.991, acc: 0.389\n", "[node-0]: Epoch: 5 [19200/60000 (32%)] \tTrain loss: 1.986, acc: 0.392\n", "[node-0]: Epoch: 5, time: 6.06 seconds\n", "[node-0]: Epoch: 6 [0/60000 (0%)] \tTrain loss: 1.895, acc: 0.404\n", "[node-0]: Epoch: 6 [1280/60000 (2%)] \tTrain loss: 1.895, acc: 0.429\n", "[node-0]: Epoch: 6 [2560/60000 (4%)] \tTrain loss: 1.888, acc: 0.431\n", "[node-0]: Epoch: 6 [3840/60000 (6%)] \tTrain loss: 1.876, acc: 0.441\n", "[node-0]: Epoch: 6 [5120/60000 (9%)] \tTrain loss: 1.869, acc: 0.443\n", "[node-0]: Epoch: 6 [6400/60000 (11%)] \tTrain loss: 1.862, acc: 0.444\n", "[node-0]: Epoch: 6 [7680/60000 (13%)] \tTrain loss: 1.855, acc: 0.444\n", "[node-0]: Epoch: 6 [8960/60000 (15%)] \tTrain loss: 1.850, acc: 0.445\n", "[node-0]: Epoch: 6 [10240/60000 (17%)] \tTrain loss: 1.843, acc: 0.446\n", "[node-0]: Epoch: 6 [11520/60000 (19%)] \tTrain loss: 1.838, acc: 0.448\n", "[node-0]: Epoch: 6 [12800/60000 (21%)] \tTrain loss: 1.831, acc: 0.450\n", "[node-0]: Epoch: 6 [14080/60000 (23%)] \tTrain loss: 1.823, acc: 0.453\n", "[node-0]: Epoch: 6 [15360/60000 (26%)] \tTrain loss: 1.816, acc: 0.455\n", "[node-0]: Epoch: 6 [16640/60000 (28%)] \tTrain loss: 1.809, acc: 0.458\n", "[node-0]: Epoch: 6 [17920/60000 (30%)] \tTrain loss: 1.801, acc: 0.461\n", "[node-0]: Epoch: 6 [19200/60000 (32%)] \tTrain loss: 1.793, acc: 0.463\n", "[node-0]: Epoch: 6, time: 6.54 seconds\n", "[node-0]: Epoch: 7 [0/60000 (0%)] \tTrain loss: 1.653, acc: 0.508\n", "[node-0]: Epoch: 7 [1280/60000 (2%)] \tTrain loss: 1.664, acc: 0.501\n", "[node-0]: Epoch: 7 [2560/60000 (4%)] \tTrain loss: 1.655, acc: 0.503\n", "[node-0]: Epoch: 7 [3840/60000 (6%)] \tTrain loss: 1.641, acc: 0.512\n", "[node-0]: Epoch: 7 [5120/60000 (9%)] \tTrain loss: 1.633, acc: 0.514\n", "[node-0]: Epoch: 7 [6400/60000 (11%)] \tTrain loss: 1.624, acc: 0.516\n", "[node-0]: Epoch: 7 [7680/60000 (13%)] \tTrain loss: 1.616, acc: 0.516\n", "[node-0]: Epoch: 7 [8960/60000 (15%)] \tTrain loss: 1.610, acc: 0.517\n", "[node-0]: Epoch: 7 [10240/60000 (17%)] \tTrain loss: 1.602, acc: 0.518\n", "[node-0]: Epoch: 7 [11520/60000 (19%)] \tTrain loss: 1.596, acc: 0.519\n", "[node-0]: Epoch: 7 [12800/60000 (21%)] \tTrain loss: 1.588, acc: 0.521\n", "[node-0]: Epoch: 7 [14080/60000 (23%)] \tTrain loss: 1.579, acc: 0.524\n", "[node-0]: Epoch: 7 [15360/60000 (26%)] \tTrain loss: 1.572, acc: 0.525\n", "[node-0]: Epoch: 7 [16640/60000 (28%)] \tTrain loss: 1.564, acc: 0.527\n", "[node-0]: Epoch: 7 [17920/60000 (30%)] \tTrain loss: 1.555, acc: 0.529\n", "[node-0]: Epoch: 7 [19200/60000 (32%)] \tTrain loss: 1.547, acc: 0.531\n", "[node-0]: Epoch: 7, time: 5.89 seconds\n", "[node-0]: Epoch: 8 [0/60000 (0%)] \tTrain loss: 1.388, acc: 0.578\n", "[node-0]: Epoch: 8 [1280/60000 (2%)] \tTrain loss: 1.412, acc: 0.552\n", "[node-0]: Epoch: 8 [2560/60000 (4%)] \tTrain loss: 1.404, acc: 0.556\n", "[node-0]: Epoch: 8 [3840/60000 (6%)] \tTrain loss: 1.390, acc: 0.564\n", "[node-0]: Epoch: 8 [5120/60000 (9%)] \tTrain loss: 1.384, acc: 0.565\n", "[node-0]: Epoch: 8 [6400/60000 (11%)] \tTrain loss: 1.375, acc: 0.567\n", "[node-0]: Epoch: 8 [7680/60000 (13%)] \tTrain loss: 1.367, acc: 0.569\n", "[node-0]: Epoch: 8 [8960/60000 (15%)] \tTrain loss: 1.362, acc: 0.571\n", "[node-0]: Epoch: 8 [10240/60000 (17%)] \tTrain loss: 1.356, acc: 0.572\n", "[node-0]: Epoch: 8 [11520/60000 (19%)] \tTrain loss: 1.351, acc: 0.573\n", "[node-0]: Epoch: 8 [12800/60000 (21%)] \tTrain loss: 1.344, acc: 0.576\n", "[node-0]: Epoch: 8 [14080/60000 (23%)] \tTrain loss: 1.336, acc: 0.579\n", "[node-0]: Epoch: 8 [15360/60000 (26%)] \tTrain loss: 1.330, acc: 0.581\n", "[node-0]: Epoch: 8 [16640/60000 (28%)] \tTrain loss: 1.323, acc: 0.583\n", "[node-0]: Epoch: 8 [17920/60000 (30%)] \tTrain loss: 1.315, acc: 0.585\n", "[node-0]: Epoch: 8 [19200/60000 (32%)] \tTrain loss: 1.308, acc: 0.587\n", "[node-0]: Epoch: 8, time: 5.90 seconds\n", "[node-0]: Epoch: 9 [0/60000 (0%)] \tTrain loss: 1.161, acc: 0.628\n", "[node-0]: Epoch: 9 [1280/60000 (2%)] \tTrain loss: 1.192, acc: 0.616\n", "[node-0]: Epoch: 9 [2560/60000 (4%)] \tTrain loss: 1.184, acc: 0.620\n", "[node-0]: Epoch: 9 [3840/60000 (6%)] \tTrain loss: 1.171, acc: 0.626\n", "[node-0]: Epoch: 9 [5120/60000 (9%)] \tTrain loss: 1.167, acc: 0.627\n", "[node-0]: Epoch: 9 [6400/60000 (11%)] \tTrain loss: 1.159, acc: 0.631\n", "[node-0]: Epoch: 9 [7680/60000 (13%)] \tTrain loss: 1.151, acc: 0.634\n", "[node-0]: Epoch: 9 [8960/60000 (15%)] \tTrain loss: 1.147, acc: 0.637\n", "[node-0]: Epoch: 9 [10240/60000 (17%)] \tTrain loss: 1.141, acc: 0.639\n", "[node-0]: Epoch: 9 [11520/60000 (19%)] \tTrain loss: 1.138, acc: 0.641\n", "[node-0]: Epoch: 9 [12800/60000 (21%)] \tTrain loss: 1.132, acc: 0.644\n", "[node-0]: Epoch: 9 [14080/60000 (23%)] \tTrain loss: 1.125, acc: 0.648\n", "[node-0]: Epoch: 9 [15360/60000 (26%)] \tTrain loss: 1.120, acc: 0.650\n", "[node-0]: Epoch: 9 [16640/60000 (28%)] \tTrain loss: 1.114, acc: 0.653\n", "[node-0]: Epoch: 9 [17920/60000 (30%)] \tTrain loss: 1.107, acc: 0.656\n", "[node-0]: Epoch: 9 [19200/60000 (32%)] \tTrain loss: 1.101, acc: 0.659\n", "[node-0]: Epoch: 9, time: 6.25 seconds\n"]}], "source": ["_ = TrainerClient().get_job_logs(name=job_id, follow=True)"]}, {"cell_type": "markdown", "id": "030ea83c-9b1c-477a-a3f3-72d659066678", "metadata": {}, "source": ["## Evaluate the Trained Model\n", "\n", "Since the volume is shared between the Minikube cluster and the local directory, you can evaluate the trained model directly.\n", "\n", "We will use test images from the MNIST dataset for prediction.\n", "\n", "- <span style=\"color:green\">Green label</span> indicate correct predictions.\n", "- <span style=\"color:red\">Red label</span> indicate incorrect predictions, with the correct value shown in parentheses."]}, {"cell_type": "code", "execution_count": 10, "id": "b11d5623-3425-41ea-87e7-ec6992540994", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 20 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import mlx.core as mx\n", "import mlx.nn as nn\n", "import matplotlib.pyplot as plt\n", "from mlx.data.datasets import load_mnist\n", "\n", "\n", "# Load test dataset and take the random batch from it.\n", "test_batch = (\n", "    load_mnist(train=False)\n", "    .key_transform(\"image\", lambda x: (x.astype(\"float32\") / 255.0).ravel())\n", "    .batch(20)[mx.random.randint(10, 500)]\n", ")\n", "\n", "class MLP(nn.Mo<PERSON>le):\n", "    def __init__(self, in_dims: int, hidden_dims: int, num_layers: int, out_dims: int):\n", "        super().__init__()\n", "        layer_sizes = [in_dims] + [hidden_dims] * num_layers + [out_dims]\n", "        self.layers = [\n", "            nn.Linear(idim, odim)\n", "            for idim, odim in zip(layer_sizes[:-1], layer_sizes[1:])\n", "        ]\n", "\n", "    def __call__(self, x):\n", "        for layer in self.layers[:-1]:\n", "            x = nn.relu(layer(x))\n", "        return self.layers[-1](x)\n", "\n", "# Load weights from the trained model.\n", "model = MLP(\n", "    in_dims=test_batch[\"image\"][0].shape[-1],\n", "    hidden_dims=32,\n", "    num_layers=2,\n", "    out_dims=10,\n", ").load_weights(MODEL_PATH)\n", "\n", "# Send test batch to the pre-trained MLP model.\n", "x = mx.array(test_batch[\"image\"])\n", "output = model(x)\n", "fig = plt.figure(figsize=(15, 5))\n", "for i in range(20):\n", "    # Format the input image and the model output.\n", "    image = test_batch[\"image\"][i].reshape((28, 28))\n", "    pred_label = mx.argmax(output[i])\n", "\n", "    # Add data to the plot.\n", "    ax = fig.add_subplot(4, 5, i + 1, xticks=[], yticks=[])\n", "    ax.imshow(image, cmap=\"gray\")\n", "    if test_batch[\"label\"][i] == pred_label:\n", "        ax.set_title(test_batch[\"label\"][i], color=\"green\")\n", "    else:\n", "        ax.set_title(\"{} ({})\".format(pred_label, test_batch[\"label\"][i]), color=\"red\")\n", "\n", "    ax.title.set_fontsize(20)\n", "    fig.tight_layout()\n"]}, {"cell_type": "code", "execution_count": null, "id": "673897de-adfd-4c29-84ba-4c0f81b8d25b", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cda451fb-b96a-42da-aebf-59a1d6c3ec35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "a6f1983c-243a-4e6d-aa9c-e97fbed2bfcf", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}