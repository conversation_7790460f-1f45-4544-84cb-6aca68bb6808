{"cells": [{"cell_type": "markdown", "id": "629c902e-6cd0-4475-b6ce-5d6e37d7e2f3", "metadata": {}, "source": ["# T5 LLM Fine-Tuning with DeepSpeed and Kubeflow Trainer\n", "\n", "\n", "This Notebook will fine-tune Text-to-Text Transfer Transformer (T5) with Wikihow dataset for text summarization using Kubeflow TrainJob and DeepSpeed.\n", "\n", "Pretrained T5 model: https://huggingface.co/google-t5/t5-base\n", "\n", "Wikihow dataset: https://huggingface.co/datasets/sentence-transformers/wikihow\n", "\n", "This Notebook will use **8 x V100 NVIDIA GPUs**, to fine-tune T5 model on 2 nodes (every node has 4 GPUs).\n", "\n", "**TODO (<PERSON><PERSON><PERSON><PERSON>)**: Currently, to run this Notebook you have to manualy update the container resources in the ClusterTrainingRuntime, since we don't propogate TrainJob's `resources_per_node` to the JobSet"]}, {"metadata": {}, "cell_type": "markdown", "source": ["## Install the Kubeflow SDK\n", "\n", "You need to install the Kubeflow SDK to interact with Kubeflow Trainer APIs:"], "id": "1c461b2984e77d99"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "# !pip install git+https://github.com/kubeflow/sdk.git@main#subdirectory=python", "id": "4900404c5d532bdf"}, {"metadata": {}, "cell_type": "markdown", "source": ["## <PERSON><PERSON> <PERSON><PERSON><PERSON> to <PERSON><PERSON><PERSON><PERSON> T5 with DeepSpeed\n", "\n", "We need to wrap our fine-tuning script into a function to create Kubeflow TrainJob."], "id": "47534ee4955f3ff6"}, {"metadata": {"jupyter": {"is_executing": true}}, "cell_type": "code", "source": ["def deepspeed_train_t5(args):\n", "    import os\n", "    import time\n", "    import boto3\n", "    import torch\n", "    import torch.distributed as dist\n", "    from torch.utils.data.distributed import DistributedSampler\n", "    from transformers import T5Tokenizer, T5ForConditionalGeneration\n", "    from datasets import Dataset\n", "    import deepspeed\n", "    import numpy as np\n", "\n", "    # Initialize distributed environment.\n", "    deepspeed.init_distributed(dist_backend=\"nccl\")\n", "    local_rank = int(os.environ[\"LOCAL_RANK\"])\n", "\n", "    # Define the Wikihow dataset class\n", "    class wikihow(torch.utils.data.Dataset):\n", "        def __init__(\n", "            self,\n", "            tokenizer,\n", "            num_samples,\n", "            input_length,\n", "            output_length,\n", "        ):\n", "            self.dataset = Dataset.from_csv(args[\"DATASET_URL\"])\n", "            self.dataset = self.dataset.select(list(range(0, num_samples)))\n", "            self.input_length = input_length\n", "            self.tokenizer = tokenizer\n", "            self.output_length = output_length\n", "\n", "        def __len__(self):\n", "            return self.dataset.shape[0]\n", "\n", "        def clean_text(self, text):\n", "            # Dataset contains empty values.\n", "            if text is None:\n", "                return \"\"\n", "            text = text.replace(\"Example of text:\", \"\")\n", "            text = text.replace(\"Example of Summary:\", \"\")\n", "            text = text.replace(\"\\n\", \"\")\n", "            text = text.replace(\"``\", \"\")\n", "            text = text.replace('\"', \"\")\n", "\n", "            return text\n", "\n", "        def convert_to_features(self, example_batch):\n", "            input_ = self.clean_text(example_batch[\"text\"])\n", "            target_ = self.clean_text(example_batch[\"headline\"])\n", "\n", "            source = self.tokenizer(\n", "                input_,\n", "                max_length=self.input_length,\n", "                padding=\"max_length\",\n", "                truncation=True,\n", "                return_tensors=\"pt\",\n", "            )\n", "            targets = self.tokenizer(\n", "                target_,\n", "                max_length=self.output_length,\n", "                padding=\"max_length\",\n", "                truncation=True,\n", "                return_tensors=\"pt\",\n", "            )\n", "\n", "            return source, targets\n", "\n", "        def __getitem__(self, index):\n", "            source, targets = self.convert_to_features(self.dataset[index])\n", "            return {\n", "                \"source_ids\": source[\"input_ids\"].squeeze(),\n", "                \"source_mask\": source[\"attention_mask\"].squeeze(),\n", "                \"target_ids\": targets[\"input_ids\"].squeeze(),\n", "                \"target_mask\": targets[\"attention_mask\"].squeeze(),\n", "            }\n", "\n", "    # Download model and tokenizer\n", "    if dist.get_rank() == 0:\n", "        print(\"-\" * 100)\n", "        print(\"Downloading T5 Model\")\n", "        print(\"-\" * 100)\n", "\n", "    model = T5ForConditionalGeneration.from_pretrained(args[\"MODEL_NAME\"])\n", "    tokenizer = T5Tokenizer.from_pretrained(args[\"MODEL_NAME\"])\n", "\n", "    # Download dataset.\n", "    dataset = wikihow(tokenizer, num_samples=1500, input_length=512, output_length=150)\n", "    train_loader = torch.utils.data.DataLoader(\n", "        dataset, batch_size=4, sampler=DistributedSampler(dataset)\n", "    )\n", "\n", "    # Define DeepSpeed configuration.\n", "    # Train batch size = micro batch size * gradient steps * GPUs (e.g. 2 x 1 x 8 = 16).\n", "    ds_config = {\n", "        \"train_micro_batch_size_per_gpu\": 2,\n", "        \"gradient_accumulation_steps\": 1,\n", "        \"fp16\": {\"enabled\": True},  # Enable mixed precision\n", "        \"optimizer\": {\n", "            \"type\": \"AdamW\",\n", "            \"params\": {\"lr\": 0.002},\n", "        },\n", "        \"scheduler\": {\n", "            \"type\": \"WarmupLR\",\n", "            \"params\": {\n", "                \"warmup_min_lr\": 0,\n", "                \"warmup_max_lr\": 0.001,\n", "                \"warmup_num_steps\": 1000,\n", "            },\n", "        },\n", "    }\n", "\n", "    # Initialize model with DeepSpeed.\n", "    model, _, _, _ = deepspeed.initialize(\n", "        config=ds_config,\n", "        model=model,\n", "        model_parameters=model.parameters(),\n", "    )\n", "\n", "    # Start training process.\n", "    if dist.get_rank() == 0:\n", "        print(\"-\" * 100)\n", "        print(\"Starting DeepSpeed distributed training...\")\n", "        print(\"-\" * 100)\n", "\n", "    t0 = time.time()\n", "    for epoch in range(1, 3):\n", "        losses = []\n", "        for batch_idx, batch in enumerate(train_loader):\n", "            for key in batch.keys():\n", "                batch[key] = batch[key].to(local_rank)\n", "            # Forward pass.\n", "            output = model(\n", "                input_ids=batch[\"source_ids\"],\n", "                attention_mask=batch[\"source_mask\"],\n", "                labels=batch[\"target_ids\"],\n", "            )\n", "            loss = output.loss\n", "\n", "            # Run backpropagation.\n", "            model.backward(loss)\n", "            # Weight updates.\n", "            model.step()\n", "            losses.append(loss.item())\n", "            if batch_idx % 10 == 0 and dist.get_rank() == 0:\n", "                print(\n", "                    \"Train Epoch: {} [{}/{} ({:.0f}%)]\\tLoss: {:.6f}\".format(\n", "                        epoch,\n", "                        batch_idx * len(batch),\n", "                        len(train_loader.dataset),\n", "                        100.0 * batch_idx / len(train_loader),\n", "                        loss.item(),\n", "                    )\n", "                )\n", "\n", "        if dist.get_rank() == 0:\n", "            print(\"-\" * 100)\n", "            print(\"Average Train Loss: {0:.4f}\".format(np.mean(losses)))\n", "            print(\"-\" * 100)\n", "\n", "    # Export model to S3.\n", "    HOME_PATH = \"/home/<USER>\"\n", "    model.save_checkpoint(save_dir=HOME_PATH)\n", "\n", "    if dist.get_rank() == 0:\n", "        print(\"-\" * 100)\n", "        print(f\"DeepSpeed training time: {int(time.time() - t0)} seconds\")\n", "        print(\"-\" * 100)\n", "\n", "        print(\"Upload T5 model to S3\")\n", "        file_path = os.path.join(HOME_PATH, \"global_step94/mp_rank_00_model_states.pt\")\n", "        bucket = boto3.resource(\"s3\").Bucket(args[\"BUCKET\"])\n", "        bucket.upload_file(file_path, f\"deepspeed/{file_path}\")"], "id": "35f06c45b614ecd0", "outputs": [], "execution_count": null}, {"cell_type": "markdown", "id": "875abc44-69dd-41d8-bb2a-436f730a2dd0", "metadata": {}, "source": ["## List Available Kubeflow Trainer Runtimes\n", "\n", "\n", "Get available Kubeflow Trainer Runtimes with the `list_runtimes()` API.\n", "\n", "You can inspect Runtime details, including the name, framework, entry point, and number of accelerators.\n", "\n", "- Runtimes with **CustomTrainer**: You must write the training script within the function.\n", "\n", "- Runtimes with **BuiltinTrainer**: You can configure settings (e.g., LoRA Config) for LLM fine-tuning Job.\n"]}, {"cell_type": "code", "execution_count": 2, "id": "51d8bc1d-8d9b-48f7-866f-c6ad4ad4241b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Name: deepspeed-distributed, Framework: deepspeed, Trainer Type: CustomTrainer\n", "\n", "Entrypoint: ['mpirun', '--hostfile', '/etc/mpi/hostfile']\n", "\n", "Runtime Accelerators: 4 x gpu-tesla-v100-16gb\n"]}], "source": ["from kubeflow.trainer import TrainerClient, CustomTrainer\n", "\n", "for r in TrainerClient().list_runtimes():\n", "    print(f\"Name: {r.name}, Framework: {r.trainer.framework.value}, Trainer Type: {r.trainer.trainer_type.value}\\n\")\n", "    print(f\"Entrypoint: {r.trainer.entrypoint[:3]}\\n\")\n", "    print(f\"Runtime Accelerators: {r.trainer.accelerator_count} x {r.trainer.accelerator}\")\n", "\n", "    if r.name == \"deepspeed-distributed\":\n", "        deepspeed_runtime = r"]}, {"cell_type": "markdown", "id": "206c389d-9dcb-474f-b0f5-cc098de2e183", "metadata": {}, "source": ["## Create <PERSON><PERSON><PERSON> for Distributed Training\n", "\n", "Use the `train()` API to scale the training code across 2 Nodes and 8 GPUs.\n", "\n", "Don't forget to update the S3 bucket name."]}, {"cell_type": "code", "execution_count": null, "id": "76dab189-f184-4e48-be74-f32c0dea675b", "metadata": {}, "outputs": [], "source": ["MODEL_NAME = \"t5-base\"\n", "BUCKET_NAME = \"TODO: add your bucket here\"\n", "args = {\n", "    \"DATASET_URL\": \"https://public-nlp-datasets.s3.us-west-2.amazonaws.com/wikihowAll.csv\",\n", "    \"MODEL_NAME\": MODEL_NAME,\n", "    \"BUCKET\": BUCKET_NAME\n", "}\n", "\n", "job_id = TrainerClient().train(\n", "    trainer=CustomTrainer(\n", "        func=deepspeed_train_t5,\n", "        func_args=args,\n", "        packages_to_install=[\"boto3\"], # Custom packages to install at runtime.\n", "        num_nodes=2,\n", "    ),\n", "    runtime=deepspeed_runtime,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "6e1811dd-4bf2-40cf-ad35-35a87271eb21", "metadata": {}, "outputs": [{"data": {"text/plain": ["'f30e8ee53855'"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Train API generates a random TrainJob id.\n", "job_id"]}, {"cell_type": "markdown", "id": "f0864121-895f-4e5a-87b8-7ea2d92e6630", "metadata": {}, "source": ["## Check the TrainJob Info\n", "\n", "Use the `list_jobs()` and `get_job()` APIs to get information about created TrainJob and its steps."]}, {"cell_type": "code", "execution_count": 5, "id": "8a945930-6cfe-4388-8ab8-462474f3f21f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TrainJob: f30e8ee53855, Status: Created, Created at: 2025-03-22 04:19:34+00:00\n"]}], "source": ["for job in TrainerClient().list_jobs():\n", "    print(f\"TrainJob: {job.name}, Status: {job.status}, Created at: {job.creation_timestamp}\")"]}, {"cell_type": "code", "execution_count": 6, "id": "75e8c741-6d31-49a2-8667-d38e40d62430", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Step: node-0, Status: Running, Devices: gpu x 4\n", "\n", "Step: node-1, Status: Running, Devices: gpu x 4\n", "\n"]}], "source": ["# We execute mpirun command on node-0, which functions as the MPI Launcher node.\n", "for c in TrainerClient().get_job(name=job_id).steps:\n", "    print(f\"Step: {c.name}, Status: {c.status}, Devices: {c.device} x {c.device_count}\\n\")"]}, {"cell_type": "markdown", "id": "d4fa3ea2-bdf5-40ca-a9ec-cc948949ea59", "metadata": {}, "source": ["## Get the TrainJob Logs\n", "\n", "Use the `get_job_logs()` API to retrieve the TrainJob logs.\n", "\n", "Since we distribute the dataset accross 8 GPUs (2 nodes x 4 GPUs), each rank processes `round(1500 / 8) = 160` samples."]}, {"cell_type": "code", "execution_count": 12, "id": "6e630fd3-f061-4fea-8024-7bffcefb257c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[node-0]: Warning: Permanently added '[f30e8ee53855-node-0-0.f30e8ee53855]:2222' (ECDSA) to the list of known hosts.\n", "[node-0]: --------------------------------------------------------------------------\n", "[node-0]: PMIx was unable to find a usable compression library\n", "[node-0]: on the system. We will therefore be unable to compress\n", "[node-0]: large data streams. This may result in longer-than-normal\n", "[node-0]: startup times and larger memory footprints. We will\n", "[node-0]: continue, but strongly recommend installing zlib or\n", "[node-0]: a comparable compression library for better user experience.\n", "[node-0]: You can suppress this warning by adding \"pcompress_base_silence_warning=1\"\n", "[node-0]: to your PMIx MCA default parameter file, or by adding\n", "[node-0]: \"PMIX_MCA_pcompress_base_silence_warning=1\" to your environment.\n", "[node-0]: --------------------------------------------------------------------------\n", "[node-0]: \u00001 more process has sent help message help-pcompress.txt / unavailable\n", "[node-0]: \u00001 more process has sent help message help-pcompress.txt / unavailable\n", "[node-0]: [2025-03-22 04:20:03,344] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:03,344] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:03,557] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:03,558] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:03,558] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:03,677] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:03,679] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:03,772] [INFO] [real_accelerator.py:222:get_accelerator] Setting ds_accelerator to cuda (auto detect)\n", "[node-0]: [2025-03-22 04:20:04,507] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,507] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,508] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,508] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,729] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,729] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,729] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,729] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,729] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,729] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,828] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,828] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,832] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,832] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,923] [INFO] [comm.py:658:init_distributed] cdb=None\n", "[node-0]: [2025-03-22 04:20:04,924] [INFO] [comm.py:673:init_distributed] Not using the DeepSpeed or dist launchers, attempting to detect MPI environment...\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=3, local_rank=3, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=0, local_rank=0, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=2, local_rank=2, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=1, local_rank=1, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:689:init_distributed] Initializing TorchBackend in DeepSpeed with backend nccl\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=7, local_rank=3, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=4, local_rank=0, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=6, local_rank=2, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: [2025-03-22 04:20:04,972] [INFO] [comm.py:728:mpi_discovery] Discovered MPI settings of world_rank=5, local_rank=1, world_size=8, master_addr=240.56.166.244, master_port=29500\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: Downloading T5 Model\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: \u0000You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "[node-0]: You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "[node-0]: You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "[node-0]: You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "[node-0]: You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "[node-0]: You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "[node-0]: You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "[node-0]: You are using the default legacy behaviour of the <class 'transformers.models.t5.tokenization_t5.T5Tokenizer'>. This is expected, and simply means that the `legacy` (previous) behavior will be used so nothing changes for you. If you want to use the new behaviour, set `legacy=False`. This should only be set if you understand what it means, and thoroughly read the reason why this was added as explained in https://github.com/huggingface/transformers/pull/24565\n", "Downloading data: 100%|██████████| 619M/619M [00:06<00:00, 90.1MB/s] \n", "Downloading data: 100%|██████████| 619M/619M [00:06<00:00, 90.6MB/s]\n", "Generating train split: 215365 examples [00:07, 27809.27 examples/s]\n", "[node-0]: [2025-03-22 04:20:26,213] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "[node-0]: [2025-03-22 04:20:26,233] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "[node-0]: [2025-03-22 04:20:26,236] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "Generating train split: 215365 examples [00:07, 27632.33 examples/s]\n", "[node-0]: [2025-03-22 04:20:26,266] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "[node-0]: [2025-03-22 04:20:26,284] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "[node-0]: [2025-03-22 04:20:26,286] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "[node-0]: [2025-03-22 04:20:26,292] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "[node-0]: [2025-03-22 04:20:26,302] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed info: version=0.16.4, git-hash=unknown, git-branch=unknown\n", "[node-0]: [2025-03-22 04:20:26,302] [INFO] [config.py:734:__init__] Config mesh_device None world_size = 8\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: Creating extension directory /home/<USER>/.cache/torch_extensions/py310_cu124/fused_adam...\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: Detected CUDA files, patching ldflags\n", "[node-0]: Emitting ninja build file /home/<USER>/.cache/torch_extensions/py310_cu124/fused_adam/build.ninja...\n", "[node-0]: /usr/local/lib/python3.10/dist-packages/torch/utils/cpp_extension.py:1964: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. \n", "[node-0]: If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].\n", "[node-0]:   warnings.warn(\n", "[node-0]: Building extension module fused_adam...\n", "[node-0]: Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "[node-0]: [2025-03-22 04:20:28,376] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Flops Profiler Enabled: False\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: Creating extension directory /home/<USER>/.cache/torch_extensions/py310_cu124/fused_adam...\n", "[node-0]: Creating extension directory /home/<USER>/.cache/torch_extensions/py310_cu124/fused_adam...\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: Detected CUDA files, patching ldflags\n", "[node-0]: Emitting ninja build file /home/<USER>/.cache/torch_extensions/py310_cu124/fused_adam/build.ninja...\n", "[node-0]: /usr/local/lib/python3.10/dist-packages/torch/utils/cpp_extension.py:1964: UserWarning: TORCH_CUDA_ARCH_LIST is not set, all archs for visible cards are included for compilation. \n", "[node-0]: If this is not desired, please set os.environ['TORCH_CUDA_ARCH_LIST'].\n", "[node-0]:   warnings.warn(\n", "[node-0]: Building extension module fused_adam...\n", "[node-0]: Allowing ninja to set a default number of workers... (overridable by setting the environment variable MAX_JOBS=N)\n", "[node-0]: Using /home/<USER>/.cache/torch_extensions/py310_cu124 as PyTorch extensions root...\n", "[node-0]: [1/3] /usr/local/cuda/bin/nvcc --generate-dependencies-with-compile --dependency-output multi_tensor_adam.cuda.o.d -DTORCH_EXTENSION_NAME=fused_adam -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\\\"_gcc\\\" -DPYBIND11_STDLIB=\\\"_libstdcpp\\\" -DPYBIND11_BUILD_ABI=\\\"_cxxabi1011\\\" -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/includes -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam -isystem /usr/local/lib/python3.10/dist-packages/torch/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/torch/csrc/api/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/TH -isystem /usr/local/lib/python3.10/dist-packages/torch/include/THC -isystem /usr/local/cuda/include -isystem /usr/include/python3.10 -D_GLIBCXX_USE_CXX11_ABI=0 -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -gencode=arch=compute_70,code=compute_70 -gencode=arch=compute_70,code=sm_70 --compiler-options '-fPIC' -O3 -DVERSION_GE_1_1 -DVERSION_GE_1_3 -DVERSION_GE_1_5 -lineinfo --use_fast_math -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_70,code=compute_70 -std=c++17 -c /usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam/multi_tensor_adam.cu -o multi_tensor_adam.cuda.o \n", "[node-0]: [1/3] /usr/local/cuda/bin/nvcc --generate-dependencies-with-compile --dependency-output multi_tensor_adam.cuda.o.d -DTORCH_EXTENSION_NAME=fused_adam -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\\\"_gcc\\\" -DPYBIND11_STDLIB=\\\"_libstdcpp\\\" -DPYBIND11_BUILD_ABI=\\\"_cxxabi1011\\\" -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/includes -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam -isystem /usr/local/lib/python3.10/dist-packages/torch/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/torch/csrc/api/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/TH -isystem /usr/local/lib/python3.10/dist-packages/torch/include/THC -isystem /usr/local/cuda/include -isystem /usr/include/python3.10 -D_GLIBCXX_USE_CXX11_ABI=0 -D__CUDA_NO_HALF_OPERATORS__ -D__CUDA_NO_HALF_CONVERSIONS__ -D__CUDA_NO_BFLOAT16_CONVERSIONS__ -D__CUDA_NO_HALF2_OPERATORS__ --expt-relaxed-constexpr -gencode=arch=compute_70,code=compute_70 -gencode=arch=compute_70,code=sm_70 --compiler-options '-fPIC' -O3 -DVERSION_GE_1_1 -DVERSION_GE_1_3 -DVERSION_GE_1_5 -lineinfo --use_fast_math -gencode=arch=compute_70,code=sm_70 -gencode=arch=compute_70,code=compute_70 -std=c++17 -c /usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam/multi_tensor_adam.cu -o multi_tensor_adam.cuda.o \n", "[node-0]: [2/3] c++ -<PERSON><PERSON> -MF fused_adam_frontend.o.d -DTORCH_EXTENSION_NAME=fused_adam -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\\\"_gcc\\\" -DPYBIND11_STDLIB=\\\"_libstdcpp\\\" -DPYBIND11_BUILD_ABI=\\\"_cxxabi1011\\\" -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/includes -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam -isystem /usr/local/lib/python3.10/dist-packages/torch/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/torch/csrc/api/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/TH -isystem /usr/local/lib/python3.10/dist-packages/torch/include/THC -isystem /usr/local/cuda/include -isystem /usr/include/python3.10 -D_GLIBCXX_USE_CXX11_ABI=0 -fPIC -std=c++17 -O3 -std=c++17 -g -Wno-reorder -DVERSION_GE_1_1 -DVERSION_GE_1_3 -DVERSION_GE_1_5 -c /usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam/fused_adam_frontend.cpp -o fused_adam_frontend.o \n", "[node-0]: [2/3] c++ -<PERSON><PERSON> -MF fused_adam_frontend.o.d -DTORCH_EXTENSION_NAME=fused_adam -DTORCH_API_INCLUDE_EXTENSION_H -DPYBIND11_COMPILER_TYPE=\\\"_gcc\\\" -DPYBIND11_STDLIB=\\\"_libstdcpp\\\" -DPYBIND11_BUILD_ABI=\\\"_cxxabi1011\\\" -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/includes -I/usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam -isystem /usr/local/lib/python3.10/dist-packages/torch/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/torch/csrc/api/include -isystem /usr/local/lib/python3.10/dist-packages/torch/include/TH -isystem /usr/local/lib/python3.10/dist-packages/torch/include/THC -isystem /usr/local/cuda/include -isystem /usr/include/python3.10 -D_GLIBCXX_USE_CXX11_ABI=0 -fPIC -std=c++17 -O3 -std=c++17 -g -Wno-reorder -DVERSION_GE_1_1 -DVERSION_GE_1_3 -DVERSION_GE_1_5 -c /usr/local/lib/python3.10/dist-packages/deepspeed/ops/csrc/adam/fused_adam_frontend.cpp -o fused_adam_frontend.o \n", "[node-0]: [3/3] c++ fused_adam_frontend.o multi_tensor_adam.cuda.o -shared -L/usr/local/lib/python3.10/dist-packages/torch/lib -lc10 -lc10_cuda -ltorch_cpu -ltorch_cuda -ltorch -ltorch_python -L/usr/local/cuda/lib64 -lcudart -o fused_adam.so\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Time to load fused_adam op: 51.523253202438354 seconds\n", "[node-0]: [2025-03-22 04:21:19,947] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Time to load fused_adam op: 51.58236646652222 seconds\n", "[node-0]: [2025-03-22 04:21:19,961] [INFO] [logging.py:128:log_dist] [Rank 0] Using DeepSpeed Optimizer param name adamw as basic optimizer\n", "[node-0]: Time to load fused_adam op: 51.582358598709106 seconds\n", "[node-0]: [2025-03-22 04:21:19,962] [INFO] [logging.py:128:log_dist] [Rank 0] Removing param_group that has no 'params' in the basic Optimizer\n", "[node-0]: [2025-03-22 04:21:19,976] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Basic Optimizer = FusedAdam\n", "[node-0]: [2025-03-22 04:21:19,977] [INFO] [logging.py:128:log_dist] [Rank 0] Creating fp16 optimizer with dynamic loss scale\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Time to load fused_adam op: 51.58146262168884 seconds\n", "[node-0]: [2025-03-22 04:21:20,006] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: [2025-03-22 04:21:20,011] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed Final Optimizer = FP16_Optimizer\n", "[node-0]: [2025-03-22 04:21:20,011] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: [2025-03-22 04:21:20,011] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed using configured LR scheduler = WarmupLR\n", "[node-0]: [2025-03-22 04:21:20,011] [INFO] [logging.py:128:log_dist] [Rank 0] DeepSpeed LR Scheduler = <deepspeed.runtime.lr_schedules.WarmupLR object at 0x7f8e08873a60>\n", "[node-0]: [2025-03-22 04:21:20,011] [INFO] [logging.py:128:log_dist] [Rank 0] step=0, skipped=0, lr=[0], mom=[(0.9, 0.999)]\n", "[node-0]: [2025-03-22 04:21:20,013] [INFO] [config.py:1001:print] DeepSpeedEngine configuration:\n", "[node-0]: [2025-03-22 04:21:20,014] [INFO] [config.py:1005:print]   activation_checkpointing_config  {\n", "[node-0]:     \"partition_activations\": false, \n", "[node-0]:     \"contiguous_memory_optimization\": false, \n", "[node-0]:     \"cpu_checkpointing\": false, \n", "[node-0]:     \"number_checkpoints\": null, \n", "[node-0]:     \"synchronize_checkpoint_boundary\": false, \n", "[node-0]:     \"profile\": false\n", "[node-0]: }\n", "[node-0]: [2025-03-22 04:21:20,014] [INFO] [config.py:1005:print]   aio_config ................... {'block_size': 1048576, 'queue_depth': 8, 'intra_op_parallelism': 1, 'single_submit': False, 'overlap_events': True, 'use_gds': False}\n", "[node-0]: [2025-03-22 04:21:20,014] [INFO] [config.py:1005:print]   amp_enabled .................. False\n", "[node-0]: [2025-03-22 04:21:20,014] [INFO] [config.py:1005:print]   amp_params ................... False\n", "[node-0]: [2025-03-22 04:21:20,015] [INFO] [config.py:1005:print]   autotuning_config ............ {\n", "[node-0]:     \"enabled\": false, \n", "[node-0]:     \"start_step\": null, \n", "[node-0]:     \"end_step\": null, \n", "[node-0]:     \"metric_path\": null, \n", "[node-0]:     \"arg_mappings\": null, \n", "[node-0]:     \"metric\": \"throughput\", \n", "[node-0]:     \"model_info\": null, \n", "[node-0]:     \"results_dir\": \"autotuning_results\", \n", "[node-0]:     \"exps_dir\": \"autotuning_exps\", \n", "[node-0]:     \"overwrite\": true, \n", "[node-0]:     \"fast\": true, \n", "[node-0]:     \"start_profile_step\": 3, \n", "[node-0]:     \"end_profile_step\": 5, \n", "[node-0]:     \"tuner_type\": \"gridsearch\", \n", "[node-0]:     \"tuner_early_stopping\": 5, \n", "[node-0]:     \"tuner_num_trials\": 50, \n", "[node-0]:     \"model_info_path\": null, \n", "[node-0]:     \"mp_size\": 1, \n", "[node-0]:     \"max_train_batch_size\": null, \n", "[node-0]:     \"min_train_batch_size\": 1, \n", "[node-0]:     \"max_train_micro_batch_size_per_gpu\": 1.024000e+03, \n", "[node-0]:     \"min_train_micro_batch_size_per_gpu\": 1, \n", "[node-0]:     \"num_tuning_micro_batch_sizes\": 3\n", "[node-0]: }\n", "[node-0]: [2025-03-22 04:21:20,015] [INFO] [config.py:1005:print]   bfloat16_enabled ............. False\n", "[node-0]: [2025-03-22 04:21:20,015] [INFO] [config.py:1005:print]   bfloat16_immediate_grad_update  False\n", "[node-0]: [2025-03-22 04:21:20,015] [INFO] [config.py:1005:print]   checkpoint_parallel_write_pipeline  False\n", "[node-0]: [2025-03-22 04:21:20,015] [INFO] [config.py:1005:print]   checkpoint_tag_validation_enabled  True\n", "[node-0]: [2025-03-22 04:21:20,015] [INFO] [config.py:1005:print]   checkpoint_tag_validation_fail  False\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   comms_config ................. <deepspeed.comm.config.DeepSpeedCommsConfig object at 0x7f8e088707f0>\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   communication_data_type ...... None\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   compression_config ........... {'weight_quantization': {'shared_parameters': {'enabled': False, 'quantizer_kernel': False, 'schedule_offset': 0, 'quantize_groups': 1, 'quantize_verbose': False, 'quantization_type': 'symmetric', 'quantize_weight_in_forward': False, 'rounding': 'nearest', 'fp16_mixed_quantize': False, 'quantize_change_ratio': 0.001}, 'different_groups': {}}, 'activation_quantization': {'shared_parameters': {'enabled': False, 'quantization_type': 'symmetric', 'range_calibration': 'dynamic', 'schedule_offset': 1000}, 'different_groups': {}}, 'sparse_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'row_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'head_pruning': {'shared_parameters': {'enabled': False, 'method': 'topk', 'schedule_offset': 1000}, 'different_groups': {}}, 'channel_pruning': {'shared_parameters': {'enabled': False, 'method': 'l1', 'schedule_offset': 1000}, 'different_groups': {}}, 'layer_reduction': {'enabled': False}}\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   curriculum_enabled_legacy .... False\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   curriculum_params_legacy ..... False\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   data_efficiency_config ....... {'enabled': False, 'seed': 1234, 'data_sampling': {'enabled': False, 'num_epochs': 1000, 'num_workers': 0, 'curriculum_learning': {'enabled': False}}, 'data_routing': {'enabled': False, 'random_ltd': {'enabled': False, 'layer_token_lr_schedule': {'enabled': False}}}}\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   data_efficiency_enabled ...... False\n", "[node-0]: [2025-03-22 04:21:20,016] [INFO] [config.py:1005:print]   dataloader_drop_last ......... False\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   disable_allgather ............ False\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   dump_state ................... False\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   dynamic_loss_scale_args ...... None\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   eigenvalue_enabled ........... False\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   eigenvalue_gas_boundary_resolution  1\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   eigenvalue_layer_name ........ bert.encoder.layer\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   eigenvalue_layer_num ......... 0\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   eigenvalue_max_iter .......... 100\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   eigenvalue_stability ......... 1e-06\n", "[node-0]: [2025-03-22 04:21:20,017] [INFO] [config.py:1005:print]   eigenvalue_tol ............... 0.01\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   eigenvalue_verbose ........... False\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   elasticity_enabled ........... False\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   flops_profiler_config ........ {\n", "[node-0]:     \"enabled\": false, \n", "[node-0]:     \"recompute_fwd_factor\": 0.0, \n", "[node-0]:     \"profile_step\": 1, \n", "[node-0]:     \"module_depth\": -1, \n", "[node-0]:     \"top_modules\": 1, \n", "[node-0]:     \"detailed\": true, \n", "[node-0]:     \"output_file\": null\n", "[node-0]: }\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   fp16_auto_cast ............... False\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   fp16_enabled ................. True\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   fp16_master_weights_and_gradients  False\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   global_rank .................. 0\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   grad_accum_dtype ............. None\n", "[node-0]: [2025-03-22 04:21:20,018] [INFO] [config.py:1005:print]   gradient_accumulation_steps .. 1\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   gradient_clipping ............ 0.0\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   gradient_predivide_factor .... 1.0\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   graph_harvesting ............. False\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   hybrid_engine ................ enabled=False max_out_tokens=512 inference_tp_size=1 release_inference_cache=False pin_parameters=True tp_gather_partition_size=8\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   initial_dynamic_scale ........ 65536\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   load_universal_checkpoint .... False\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   loss_scale ................... 0\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   memory_breakdown ............. False\n", "[node-0]: [2025-03-22 04:21:20,019] [INFO] [config.py:1005:print]   mics_hierarchial_params_gather  False\n", "[node-0]: [2025-03-22 04:21:20,020] [INFO] [config.py:1005:print]   mics_shard_size .............. -1\n", "[node-0]: [2025-03-22 04:21:20,020] [INFO] [config.py:1005:print]   monitor_config ............... tensorboard=TensorBoardConfig(enabled=False, output_path='', job_name='DeepSpeedJobName') comet=CometConfig(enabled=False, samples_log_interval=100, project=None, workspace=None, api_key=None, experiment_name=None, experiment_key=None, online=None, mode=None) wandb=WandbConfig(enabled=False, group=None, team=None, project='deepspeed') csv_monitor=CSVConfig(enabled=False, output_path='', job_name='DeepSpeedJobName')\n", "[node-0]: [2025-03-22 04:21:20,020] [INFO] [config.py:1005:print]   nebula_config ................ {\n", "[node-0]:     \"enabled\": false, \n", "[node-0]:     \"persistent_storage_path\": null, \n", "[node-0]:     \"persistent_time_interval\": 100, \n", "[node-0]:     \"num_of_version_in_retention\": 2, \n", "[node-0]:     \"enable_nebula_load\": true, \n", "[node-0]:     \"load_path\": null\n", "[node-0]: }\n", "[node-0]: [2025-03-22 04:21:20,020] [INFO] [config.py:1005:print]   optimizer_legacy_fusion ...... False\n", "[node-0]: [2025-03-22 04:21:20,020] [INFO] [config.py:1005:print]   optimizer_name ............... adamw\n", "[node-0]: [2025-03-22 04:21:20,020] [INFO] [config.py:1005:print]   optimizer_params ............. {'lr': 0.002}\n", "[node-0]: [2025-03-22 04:21:20,020] [INFO] [config.py:1005:print]   pipeline ..................... {'stages': 'auto', 'partition': 'best', 'seed_layers': False, 'activation_checkpoint_interval': 0, 'pipe_partitioned': True, 'grad_partitioned': True}\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   pld_enabled .................. False\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   pld_params ................... False\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   prescale_gradients ........... False\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   scheduler_name ............... WarmupLR\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   scheduler_params ............. {'warmup_min_lr': 0, 'warmup_max_lr': 0.001, 'warmup_num_steps': 1000}\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   seq_parallel_communication_data_type  torch.float32\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   sparse_attention ............. None\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   sparse_gradients_enabled ..... False\n", "[node-0]: [2025-03-22 04:21:20,021] [INFO] [config.py:1005:print]   steps_per_print .............. None\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   tensor_parallel_config ....... dtype=torch.float16 autotp_size=0 tensor_parallel=TPConfig(tp_size=1, tp_grain_size=1, mpu=None, tp_group=None) injection_policy_tuple=None keep_module_on_host=False replace_with_kernel_inject=False\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   timers_config ................ enabled=True synchronized=True\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   train_batch_size ............. 16\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   train_micro_batch_size_per_gpu  2\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   use_data_before_expert_parallel_  False\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   use_node_local_storage ....... False\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   wall_clock_breakdown ......... False\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   weight_quantization_config ... None\n", "[node-0]: [2025-03-22 04:21:20,022] [INFO] [config.py:1005:print]   world_size ................... 8\n", "[node-0]: [2025-03-22 04:21:20,023] [INFO] [config.py:1005:print]   zero_allow_untested_optimizer  False\n", "[node-0]: [2025-03-22 04:21:20,023] [INFO] [config.py:1005:print]   zero_config .................. stage=0 contiguous_gradients=True reduce_scatter=True reduce_bucket_size=500000000 use_multi_rank_bucket_allreduce=True allgather_partitions=True allgather_bucket_size=500000000 overlap_comm=False load_from_fp32_weights=True elastic_checkpoint=False offload_param=None offload_optimizer=None sub_group_size=1000000000 cpu_offload_param=None cpu_offload_use_pin_memory=None cpu_offload=None prefetch_bucket_size=50000000 param_persistence_threshold=100000 model_persistence_threshold=9223372036854775807 max_live_parameters=1000000000 max_reuse_distance=1000000000 gather_16bit_weights_on_model_save=False module_granularity_threshold=0 use_all_reduce_for_fetch_params=False stage3_gather_fp16_weights_on_model_save=False ignore_unused_parameters=True legacy_stage1=False round_robin_gradients=False zero_hpz_partition_size=1 zero_quantized_weights=False zero_quantized_nontrainable_weights=False zero_quantized_gradients=False zeropp_loco_param=None mics_shard_size=-1 mics_hierarchical_params_gather=False memory_efficient_linear=True pipeline_loading_checkpoint=False override_module_apply=True log_trace_cache_warnings=False\n", "[node-0]: [2025-03-22 04:21:20,023] [INFO] [config.py:1005:print]   zero_enabled ................. False\n", "[node-0]: [2025-03-22 04:21:20,023] [INFO] [config.py:1005:print]   zero_force_ds_cpu_optimizer .. True\n", "[node-0]: [2025-03-22 04:21:20,023] [INFO] [config.py:1005:print]   zero_optimization_stage ...... 0\n", "[node-0]: [2025-03-22 04:21:20,023] [INFO] [config.py:991:print_user_config]   json = {\n", "[node-0]:     \"train_micro_batch_size_per_gpu\": 2, \n", "[node-0]:     \"gradient_accumulation_steps\": 1, \n", "[node-0]:     \"fp16\": {\n", "[node-0]:         \"enabled\": true\n", "[node-0]:     }, \n", "[node-0]:     \"optimizer\": {\n", "[node-0]:         \"type\": \"AdamW\", \n", "[node-0]:         \"params\": {\n", "[node-0]:             \"lr\": 0.002\n", "[node-0]:         }\n", "[node-0]:     }, \n", "[node-0]:     \"scheduler\": {\n", "[node-0]:         \"type\": \"WarmupLR\", \n", "[node-0]:         \"params\": {\n", "[node-0]:             \"warmup_min_lr\": 0, \n", "[node-0]:             \"warmup_max_lr\": 0.001, \n", "[node-0]:             \"warmup_num_steps\": 1000\n", "[node-0]:         }\n", "[node-0]:     }\n", "[node-0]: }\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: Starting DeepSpeed distributed training...\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: [2025-03-22 04:21:20,038] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: [3/3] c++ fused_adam_frontend.o multi_tensor_adam.cuda.o -shared -L/usr/local/lib/python3.10/dist-packages/torch/lib -lc10 -lc10_cuda -ltorch_cpu -ltorch_cuda -ltorch -ltorch_python -L/usr/local/cuda/lib64 -lcudart -o fused_adam.so\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Time to load fused_adam op: 52.0293972492218 seconds\n", "[node-0]: [2025-03-22 04:21:20,426] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Time to load fused_adam op: 52.08422088623047 seconds\n", "[node-0]: Time to load fused_adam op: 52.081968784332275 seconds\n", "[node-0]: Loading extension module fused_adam...\n", "[node-0]: Time to load fused_adam op: 52.08312654495239 seconds\n", "[node-0]: [2025-03-22 04:21:20,492] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: [2025-03-22 04:21:20,492] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: [2025-03-22 04:21:20,512] [WARNING] [lr_schedules.py:683:get_lr] Attempting to get learning rate from scheduler before it has started\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: Passing a tuple of `past_key_values` is deprecated and will be removed in Transformers v4.48.0. You should pass an instance of `EncoderDecoderCache` instead, e.g. `past_key_values=EncoderDecoderCache.from_legacy_cache(past_key_values)`.\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:21,506] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 65536, reducing to 32768.0\n", "[node-0]: Train Epoch: 1 [0/1500 (0%)]\tLoss: 14.679688\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 0\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:21,512] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 65536 to 32768.0\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,074] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 32768.0, reducing to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 1\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,076] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 32768.0 to 16384.0\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,645] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 2\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 16384.0 to 8192.0\n", "[node-0]: [2025-03-22 04:21:22,652] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 16384.0, reducing to 8192.0\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,205] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,211] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,211] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,211] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,211] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 3\n", "[node-0]: [2025-03-22 04:21:23,212] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,212] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,212] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,212] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 8192.0 to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,212] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 8192.0, reducing to 4096.0\n", "[node-0]: [2025-03-22 04:21:23,772] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,772] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,772] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,772] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,772] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,772] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,773] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 4096.0, reducing to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,773] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,773] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 4\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:23,779] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 4096.0 to 2048.0\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,333] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 2048.0, reducing to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,334] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,334] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,335] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,334] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,335] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,334] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 5\n", "[node-0]: [2025-03-22 04:21:24,335] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,335] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 2048.0 to 1024.0\n", "[node-0]: [2025-03-22 04:21:24,893] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,893] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,893] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:24,894] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:24,893] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,894] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:24,894] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,894] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:24,894] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 1024.0, reducing to 512.0\n", "[node-0]: [2025-03-22 04:21:24,895] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,895] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:24,895] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,895] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,896] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:24,896] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:24,896] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 6\n", "[node-0]: [2025-03-22 04:21:24,896] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 1024.0 to 512.0\n", "[node-0]: [2025-03-22 04:21:26,713] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,713] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: [2025-03-22 04:21:26,713] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,714] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: [2025-03-22 04:21:26,714] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,714] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: [2025-03-22 04:21:26,714] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,714] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: [2025-03-22 04:21:26,714] [INFO] [logging.py:128:log_dist] [Rank 0] Overflow detected. Skipping step. Attempted loss scale: 512.0, reducing to 256.0\n", "[node-0]: [2025-03-22 04:21:26,715] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,715] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: [2025-03-22 04:21:26,715] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,715] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,715] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: [2025-03-22 04:21:26,715] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: [2025-03-22 04:21:26,716] [INFO] [fused_optimizer.py:392:_update_scale] \n", "[node-0]: Grad overflow on iteration 9\n", "[node-0]: [2025-03-22 04:21:26,716] [INFO] [fused_optimizer.py:393:_update_scale] Reducing dynamic loss scale from 512.0 to 256.0\n", "[node-0]: Train Epoch: 1 [40/1500 (21%)]\tLoss: 14.070312\n", "[node-0]: Train Epoch: 1 [80/1500 (43%)]\tLoss: 2.314453\n", "[node-0]: Train Epoch: 1 [120/1500 (64%)]\tLoss: 1.629883\n", "[node-0]: Train Epoch: 1 [160/1500 (85%)]\tLoss: 1.478516\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: Average Train Loss: 4.4380\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: Train Epoch: 2 [0/1500 (0%)]\tLoss: 1.138672\n", "[node-0]: Train Epoch: 2 [40/1500 (21%)]\tLoss: 1.253906\n", "[node-0]: Train Epoch: 2 [80/1500 (43%)]\tLoss: 1.686523\n", "[node-0]: Train Epoch: 2 [120/1500 (64%)]\tLoss: 1.251953\n", "[node-0]: Train Epoch: 2 [160/1500 (85%)]\tLoss: 1.210938\n", "[node-0]: [rank5]:[W322 04:22:17.603286655 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 5]  using GPU 1 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: [rank6]:[W322 04:22:17.603914291 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 6]  using GPU 2 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: [rank7]:[W322 04:22:17.604063023 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 7]  using GPU 3 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: [rank4]:[W322 04:22:17.604332950 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 4]  using GPU 0 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: [rank2]:[W322 04:22:17.825201730 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 2]  using GPU 2 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: [rank1]:[W322 04:22:17.825328531 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 1]  using GPU 1 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: [rank3]:[W322 04:22:17.826025393 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 3]  using GPU 3 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: Average Train Loss: 1.1029\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: [rank0]:[W322 04:22:17.826592133 ProcessGroupNCCL.cpp:4115] [PG ID 0 PG GUID 0 Rank 0]  using GPU 0 to perform barrier as devices used by this process are currently unknown. This can potentially cause a hang if this rank to GPU mapping is incorrect.Specify device_ids in barrier() to force use of a particular device,or call init_process_group() with a device_id.\n", "[node-0]: [2025-03-22 04:22:18,240] [INFO] [logging.py:128:log_dist] [Rank 0] [Torch] Checkpoint global_step94 is about to be saved!\n", "[node-0]: [2025-03-22 04:22:18,246] [INFO] [logging.py:128:log_dist] [Rank 0] Saving model checkpoint: /home/<USER>/global_step94/mp_rank_00_model_states.pt\n", "[node-0]: [2025-03-22 04:22:18,246] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: [2025-03-22 04:22:18,246] [INFO] [torch_checkpoint_engine.py:21:save] [Torch] Saving /home/<USER>/global_step94/mp_rank_00_model_states.pt...\n", "[node-0]: [2025-03-22 04:22:18,246] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: [2025-03-22 04:22:18,246] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: [2025-03-22 04:22:18,246] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: [2025-03-22 04:22:18,246] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: [2025-03-22 04:22:18,247] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: [2025-03-22 04:22:18,249] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: [2025-03-22 04:22:23,294] [INFO] [torch_checkpoint_engine.py:23:save] [Torch] Saved /home/<USER>/global_step94/mp_rank_00_model_states.pt.\n", "[node-0]: [2025-03-22 04:22:23,294] [INFO] [torch_checkpoint_engine.py:33:commit] [Torch] Checkpoint global_step94 is ready now!\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: DeepSpeed training time: 63 seconds\n", "[node-0]: ----------------------------------------------------------------------------------------------------\n", "[node-0]: Upload T5 model to S3\n", "[node-0]: [rank4]:[W322 04:22:23.569482390 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())\n", "[node-0]: [rank0]:[W322 04:22:58.465906923 ProcessGroupNCCL.cpp:1250] Warning: WARNING: process group has NOT been destroyed before we destruct ProcessGroupNCCL. On normal program exit, the application should call destroy_process_group to ensure that any pending NCCL operations have finished in this process. In rare cases this process can exit before this point and block the progress of another member of the process group. This constraint has always been present,  but this warning has only been added since PyTorch 2.4 (function operator())\n"]}], "source": ["_ = TrainerClient().get_job_logs(name=job_id, follow=True)"]}, {"cell_type": "markdown", "id": "030ea83c-9b1c-477a-a3f3-72d659066678", "metadata": {}, "source": ["## Download the Trained Model\n", "\n", "Finally, download fine-tuned model from S3 for evaluations."]}, {"cell_type": "code", "execution_count": 13, "id": "b11d5623-3425-41ea-87e7-ec6992540994", "metadata": {}, "outputs": [], "source": ["import boto3\n", "\n", "bucket = boto3.resource(\"s3\").Bucket(BUCKET_NAME)\n", "bucket.download_file(\"deepspeed/global_step94/mp_rank_00_model_states.pt\", \"deepspeed_model.pt\")"]}, {"cell_type": "markdown", "id": "3075e5d9-62ec-495e-9bc8-18b5b3269e25", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON> Fine-Tuned T5 Model\n", "\n", "After model is downloaded, you can convert it into a `state_dict` and load into the HuggingFace pipeline.\n", "\n", "The T5 model performs well for NLP tasks such as summarization, translation, and text classification.\n", "\n", "In the example below, we'll demonstrate how to use a fine-tuned version of the T5 model to summarize documentation related to the Kubeflow Trainer project."]}, {"cell_type": "code", "execution_count": null, "id": "e54caa0d-bd5a-4c3d-862b-3041072461b7", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Device set to use mps:0\n"]}, {"data": {"text/plain": ["[{'summary_text': 'Kubeflow Trainer is a Kubernetes-native project designed for large language models (LLMs) .'}]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["import torch\n", "from transformers import AutoConfig, AutoModelForSeq2SeqLM, AutoTokenizer, pipeline\n", "\n", "# Load the DeepSpeed checkpoint to state dict (DeepSpeed stores it inside 'module')\n", "ds_state_dict = torch.load(\"deepspeed_model.pt\", map_location=\"cpu\")[\"module\"]\n", "\n", "# Load state dict into HuggingFace model.\n", "config = AutoConfig.from_pretrained(MODEL_NAME)\n", "model = AutoModelForSeq2SeqLM.from_config(config)\n", "model.load_state_dict(ds_state_dict)\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)\n", "\n", "summarizer = pipeline(\"summarization\", model=model, tokenizer=tokenizer, framework=\"pt\")\n", "\n", "text = \"\"\"\n", "summarize: In Kubeflow Trainer you can integrate other ML libraries such as HuggingFace,\n", "DeepSpeed, or Megatron-LM with Kube<PERSON> Trainer to orchestrate their ML training on Kubernetes.\n", "Kubeflow Trainer allows you to effortlessly develop your LLMs with the Kubeflow Python SDK\n", "and build Kubernetes-native Training Runtimes with Kubernetes Custom Resources APIs.\n", "Kubeflow Trainer is a Kubernetes-native project designed for large language models (LLMs)\n", "fine-tuning and enabling scalable, distributed training of machine learning (ML)\n", "models across various frameworks, including PyTorch, JAX, TensorFlow, and XGBoost.\n", "\"\"\"\n", "\n", "summarizer(text, min_length=5, max_length=100)"]}, {"cell_type": "code", "execution_count": null, "id": "54c68c68-9613-400c-9937-23b54d4d4b1d", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}