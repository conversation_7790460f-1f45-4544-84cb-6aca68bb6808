repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v2.3.0
    hooks:
      - id: check-yaml
        args: [--allow-multiple-documents]
        exclude: '^charts/kubeflow-trainer/.*\.yaml$'
      - id: check-json
      - id: end-of-file-fixer
      - id: trailing-whitespace
  - repo: https://github.com/pycqa/isort
    rev: 5.11.5
    hooks:
      - id: isort
        name: isort
        entry: isort --profile black
  - repo: https://github.com/psf/black
    rev: 24.2.0
    hooks:
      - id: black
  - repo: https://github.com/pycqa/flake8
    rev: 7.1.1
    hooks:
      - id: flake8
exclude: |
  (?x)^(
    docs/images/.*|
    pkg/client/.*|
    api/python_api/kubeflow_trainer_api/models/.*|
  )$
