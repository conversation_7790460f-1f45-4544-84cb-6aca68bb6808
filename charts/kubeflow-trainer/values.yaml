#
# Copyright 2025 The Kubeflow authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

# Default values for trainer.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# -- String to partially override release name.
nameOverride: ""

# -- String to fully override release name.
fullnameOverride: ""

jobset:
  # -- Whether to install jobset as a dependency managed by trainer.
  # This must be set to `false` if jobset controller/webhook has already been installed into the cluster.
  install: true
  # -- String to fully override jobset release name.
  fullnameOverride: jobset

# -- Common labels to add to the resources.
commonLabels: {}

image:
  # -- Image registry.
  registry: ghcr.io
  # -- Image repository.
  repository: kubeflow/trainer/trainer-controller-manager
  # -- Image tag.
  tag: latest
  # -- Image pull policy.
  pullPolicy: IfNotPresent
  # -- Image pull secrets for private image registry.
  pullSecrets: []
  # - name: <secret-name>

manager:
  # -- Number of replicas of manager.
  replicas: 1

  # -- Extra labels for manager pods.
  labels: {}
    # key1: value1
    # key2: value2

  # -- Extra annotations for manager pods.
  annotations: {}
    # key1: value1
    # key2: value2

  # -- Volumes for manager pods.
  volumes: []

  # -- Node selector for manager pods.
  nodeSelector: {}

  # -- Affinity for manager pods.
  affinity: {}

  # -- List of node taints to tolerate for manager pods.
  tolerations: []

  # -- Environment variables for manager containers.
  env: []

  # -- Environment variable sources for manager containers.
  envFrom: []

  # -- Volume mounts for manager containers.
  volumeMounts: []

  # -- Pod resource requests and limits for manager containers.
  resources: {}
    # limits:
    #   cpu: 100m
    #   memory: 300Mi
    # requests:
    #   cpu: 100m
    #   memory: 300Mi

  # -- Security context for manager containers.
  securityContext: {}
    # readOnlyRootFilesystem: true
    # privileged: false
    # allowPrivilegeEscalation: false
    # runAsNonRoot: true
    # capabilities:
    #   drop:
    #   - ALL
    # seccompProfile:
    #   type: RuntimeDefault

webhook:
  # -- Specifies how unrecognized errors are handled.
  # Available options are `Ignore` or `Fail`.
  failurePolicy: Fail
