#
# Copyright 2025 The Kubeflow authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

apiVersion: v2

name: kubeflow-trainer

description: A Helm chart for deploying Kubeflow Trainer on Kubernetes.

version: 2.0.0

type: application

dependencies:
- name: jobset
  repository: file://./jobset-v0.8.1.tgz
  version: v0.8.1
  condition: jobset.install

keywords:
- kubeflow trainer

home: https://github.com/kubeflow/trainer

maintainers:
- name: andreyvelich
  url: https://github.com/andreyvelich
- name: ChenYi015
  url: https://github.com/ChenYi015
- name: gaocegege
  url: https://github.com/gaocegege
- name: <PERSON>wan
  url: https://github.com/Jeffwan
- name: johnugeorge
  url: https://github.com/johnugeorge
- name: tenzen-y
  url: https://github.com/tenzen-y
- name: terrytangyuan
  url: https://github.com/terrytangyuan
