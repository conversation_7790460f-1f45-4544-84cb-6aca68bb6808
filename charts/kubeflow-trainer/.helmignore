# Patterns to ignore when building packages.
# This supports shell glob matching, relative path matching, and
# negation (prefixed with !). Only one pattern per line.

.helmignore
ci/
Chart.lock

# helm-unittest
.debug
tests/
__snapshot__/

# helm-docs
README.md.gotmpl

# Common VCS dirs
.git/
.gitignore
.bzr/
.bzrignore
.hg/
.hgignore
.svn/

# Common backup files
*.swp
*.bak
*.tmp
*.orig
*~

# Various IDEs
*.tmproj
.project
.idea/
.vscode/

# MacOS
.DS_Store
