{{- /*
Copyright 2025 The Kubeflow authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/ -}}

{{/*
Create the name of the manager.
*/}}
{{- define "trainer.manager.name" -}}
{{ include "trainer.fullname" . }}-controller-manager
{{- end -}}

{{/*
Common labels for the manager.
*/}}
{{- define "trainer.manager.labels" -}}
{{ include "trainer.labels" . }}
app.kubernetes.io/part-of: kubeflow
app.kubernetes.io/component: manager
{{- with .Values.manager.labels }}
{{- toYaml . | nindent 0 }}
{{- end }}
{{- end -}}

{{/*
Selector labels for the manager.
*/}}
{{- define "trainer.manager.selectorLabels" -}}
{{ include "trainer.selectorLabels" . }}
app.kubernetes.io/part-of: kubeflow
app.kubernetes.io/component: manager
{{- with .Values.manager.labels }}
{{- toYaml . | nindent 0 }}
{{- end }}
{{- end -}}

{{/*
Create the name of the manager deployment.
*/}}
{{- define "trainer.manager.deployment.name" -}}
{{ include "trainer.manager.name" . }}
{{- end -}}

{{- define "trainer.manager.service.name" -}}
{{ include "trainer.manager.name" . }}
{{- end -}}
