{{- /*
Copyright 2025 The Kubeflow authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/ -}}

apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: {{ include "trainer.webhook.validatingWebhookConfiguration.name" . }}
  labels:
    {{- include "trainer.webhook.labels" . | nindent 4 }}
webhooks:
- name: validator.clustertrainingruntime.trainer.kubeflow.org
  admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: {{ include "trainer.webhook.service.name" . }}
      namespace: {{ .Release.Namespace }}
      path: /validate-trainer-kubeflow-org-v1alpha1-clustertrainingruntime
  sideEffects: None
  {{- with .Values.webhook.failurePolicy }}
  failurePolicy: {{ . }}
  {{- end }}
  rules:
  - apiGroups:
    - trainer.kubeflow.org
    apiVersions:
    - v1alpha1
    resources:
    - clustertrainingruntimes
    operations:
    - CREATE
    - UPDATE
- name: validator.trainingruntime.trainer.kubeflow.org
  admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: {{ include "trainer.webhook.service.name" . }}
      namespace: {{ .Release.Namespace }}
      path: /validate-trainer-kubeflow-org-v1alpha1-trainingruntime
  sideEffects: None
  {{- with .Values.webhook.failurePolicy }}
  failurePolicy: {{ . }}
  {{- end }}
  rules:
  - apiGroups:
    - trainer.kubeflow.org
    apiVersions:
    - v1alpha1
    resources:
    - trainingruntimes
    operations:
    - CREATE
    - UPDATE
- name: validator.trainjob.trainer.kubeflow.org
  admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: {{ include "trainer.webhook.service.name" . }}
      namespace: {{ .Release.Namespace }}
      path: /validate-trainer-kubeflow-org-v1alpha1-trainjob
  sideEffects: None
  {{- with .Values.webhook.failurePolicy }}
  failurePolicy: {{ . }}
  {{- end }}
  rules:
  - apiGroups:
    - trainer.kubeflow.org
    apiVersions:
    - v1alpha1
    resources:
    - trainjobs
    operations:
    - CREATE
    - UPDATE
