{{- /*
Copyright 2025 The Kubeflow authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    https://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/ -}}

apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "trainer.manager.clusterRole.name" . }}
  labels:
    {{- include "trainer.manager.labels" . | nindent 4 }}
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - secrets
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - validatingwebhookconfigurations
  verbs:
  - get
  - list
  - update
  - watch
- apiGroups:
  - jobset.x-k8s.io
  resources:
  - jobsets
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - scheduling.x-k8s.io
  resources:
  - podgroups
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - trainer.kubeflow.org
  resources:
  - clustertrainingruntimes
  - trainingruntimes
  - trainjobs
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - trainer.kubeflow.org
  resources:
  - clustertrainingruntimes/finalizers
  - trainingruntimes/finalizers
  - trainjobs/finalizers
  - trainjobs/status
  verbs:
  - get
  - patch
  - update
