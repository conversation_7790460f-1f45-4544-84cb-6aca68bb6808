#
# Copyright 2025 The Kubeflow authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

suite: Test manager Deployment

templates:
  - manager/deployment.yaml

release:
  name: kubeflow-trainer
  namespace: kubeflow-system

tests:
  - it: Should create manager Deployment
    asserts:
      - containsDocument:
          apiVersion: apps/v1
          kind: Deployment
          name: kubeflow-trainer-controller-manager
          namespace: kubeflow-system

  - it: Should use the specified image repository if `image.registry`, `image.repository` and `image.tag` are set
    set:
      image:
        registry: test-registry
        repository: test-repository
        tag: test-tag
    asserts:
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].image
          value: test-registry/test-repository:test-tag

  - it: Should use the specified image pull policy if `image.pullPolicy` is set
    set:
      image:
        pullPolicy: Always
    asserts:
      - equal:
          path: spec.template.spec.containers[*].imagePullPolicy
          value: Always

  - it: Should set replicas if `controller.replicas` is set
    set:
      manager:
        replicas: 10
    asserts:
      - equal:
          path: spec.replicas
          value: 10

  - it: Should set replicas if `controller.replicas` is set
    set:
      manager:
        replicas: 0
    asserts:
      - equal:
          path: spec.replicas
          value: 0

  - it: Should add pod labels if `controller.labels` is set
    set:
      manager:
        labels:
          key1: value1
          key2: value2
    asserts:
      - equal:
          path: spec.template.metadata.labels.key1
          value: value1
      - equal:
          path: spec.template.metadata.labels.key2
          value: value2

  - it: Should add environment variables if `controller.env` is set
    set:
      manager:
        env:
          - name: ENV_NAME_1
            value: ENV_VALUE_1
          - name: ENV_NAME_2
            valueFrom:
              configMapKeyRef:
                name: test-configmap
                key: test-key
                optional: false
    asserts:
      - contains:
          path: spec.template.spec.containers[?(@.name=="manager")].env
          content:
            name: ENV_NAME_1
            value: ENV_VALUE_1
      - contains:
          path: spec.template.spec.containers[?(@.name=="manager")].env
          content:
            name: ENV_NAME_2
            valueFrom:
              configMapKeyRef:
                name: test-configmap
                key: test-key
                optional: false

  - it: Should add environment variable sources if `controller.envFrom` is set
    set:
      manager:
        envFrom:
          - configMapRef:
              name: test-configmap
              optional: false
          - secretRef:
              name: test-secret
              optional: false
    asserts:
      - contains:
          path: spec.template.spec.containers[?(@.name=="manager")].envFrom
          content:
            configMapRef:
              name: test-configmap
              optional: false
      - contains:
          path: spec.template.spec.containers[?(@.name=="manager")].envFrom
          content:
            secretRef:
              name: test-secret
              optional: false

  - it: Should add volume mounts if `controller.volumeMounts` is set
    set:
      manager:
        volumeMounts:
          - name: volume1
            mountPath: /volume1
          - name: volume2
            mountPath: /volume2
    asserts:
      - contains:
          path: spec.template.spec.containers[?(@.name=="manager")].volumeMounts
          content:
            name: volume1
            mountPath: /volume1
      - contains:
          path: spec.template.spec.containers[?(@.name=="manager")].volumeMounts
          content:
            name: volume2
            mountPath: /volume2

  - it: Should add resources if `controller.resources` is set
    set:
      manager:
        resources:
          requests:
            memory: "64Mi"
            cpu: "250m"
          limits:
            memory: "128Mi"
            cpu: "500m"
    asserts:
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].resources
          value:
            requests:
              memory: "64Mi"
              cpu: "250m"
            limits:
              memory: "128Mi"
              cpu: "500m"

  - it: Should add container securityContext if `controller.securityContext` is set
    set:
      manager:
        securityContext:
          readOnlyRootFilesystem: true
          runAsUser: 1000
          runAsGroup: 2000
          fsGroup: 3000
          allowPrivilegeEscalation: false
          capabilities:
            drop:
              - ALL
          runAsNonRoot: true
          privileged: false
    asserts:
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.readOnlyRootFilesystem
          value: true
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.runAsUser
          value: 1000
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.runAsGroup
          value: 2000
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.fsGroup
          value: 3000
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.allowPrivilegeEscalation
          value: false
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.capabilities
          value:
            drop:
              - ALL
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.runAsNonRoot
          value: true
      - equal:
          path: spec.template.spec.containers[?(@.name=="manager")].securityContext.privileged
          value: false

  - it: Should add secrets if `image.pullSecrets` is set
    set:
      image:
        pullSecrets:
          - name: test-secret1
          - name: test-secret2
    asserts:
      - equal:
          path: spec.template.spec.imagePullSecrets[0].name
          value: test-secret1
      - equal:
          path: spec.template.spec.imagePullSecrets[1].name
          value: test-secret2

  - it: Should add volumes if `controller.volumes` is set
    set:
      manager:
        volumes:
          - name: volume1
            emptyDir: {}
          - name: volume2
            emptyDir: {}
    asserts:
      - contains:
          path: spec.template.spec.volumes
          content:
            name: volume1
            emptyDir: {}
          count: 1
      - contains:
          path: spec.template.spec.volumes
          content:
            name: volume2
            emptyDir: {}
          count: 1

  - it: Should add nodeSelector if `controller.nodeSelector` is set
    set:
      manager:
        nodeSelector:
          key1: value1
          key2: value2
    asserts:
      - equal:
          path: spec.template.spec.nodeSelector.key1
          value: value1
      - equal:
          path: spec.template.spec.nodeSelector.key2
          value: value2

  - it: Should add affinity if `controller.affinity` is set
    set:
      manager:
        affinity:
          nodeAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
              nodeSelectorTerms:
                - matchExpressions:
                    - key: topology.kubernetes.io/zone
                      operator: In
                      values:
                        - antarctica-east1
                        - antarctica-west1
            preferredDuringSchedulingIgnoredDuringExecution:
              - weight: 1
                preference:
                  matchExpressions:
                    - key: another-node-label-key
                      operator: In
                      values:
                        - another-node-label-value
    asserts:
      - equal:
          path: spec.template.spec.affinity
          value:
            nodeAffinity:
              requiredDuringSchedulingIgnoredDuringExecution:
                nodeSelectorTerms:
                  - matchExpressions:
                      - key: topology.kubernetes.io/zone
                        operator: In
                        values:
                          - antarctica-east1
                          - antarctica-west1
              preferredDuringSchedulingIgnoredDuringExecution:
                - weight: 1
                  preference:
                    matchExpressions:
                      - key: another-node-label-key
                        operator: In
                        values:
                          - another-node-label-value

  - it: Should add tolerations if `controller.tolerations` is set
    set:
      manager:
        tolerations:
          - key: key1
            operator: Equal
            value: value1
            effect: NoSchedule
          - key: key2
            operator: Exists
            effect: NoSchedule
    asserts:
      - equal:
          path: spec.template.spec.tolerations
          value:
            - key: key1
              operator: Equal
              value: value1
              effect: NoSchedule
            - key: key2
              operator: Exists
              effect: NoSchedule
