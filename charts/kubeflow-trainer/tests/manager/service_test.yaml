#
# Copyright 2025 The Kubeflow authors.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     https://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

suite: Test manager Service

templates:
  - manager/service.yaml

release:
  name: kubeflow-trainer
  namespace: kubeflow-system

tests:
  - it: Should create manager Service
    asserts:
      - containsDocument:
          apiVersion: v1
          kind: Service
          name: kubeflow-trainer-controller-manager
          namespace: kubeflow-system
