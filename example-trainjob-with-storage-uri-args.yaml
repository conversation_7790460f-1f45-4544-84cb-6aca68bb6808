apiVersion: trainer.kubeflow.org/v1alpha1
kind: TrainJob
metadata:
  name: llm-fine-tuning-with-args
  namespace: default
spec:
  runtimeRef:
    name: torch-distributed-with-initializers
    apiGroup: trainer.kubeflow.org
    kind: ClusterTrainingRuntime
  
  # 初始化器配置 - StorageUri 将被添加到 init container 的 args 中
  initializer:
    # 数据集初始化 - 会在 dataset-initializer container 的 args 中添加 "--storage-uri s3://my-bucket/dataset"
    dataset:
      storageUri: "s3://my-bucket/alpaca-gpt4-data-zh"
      secretRef: 
        name: s3-credentials
      env:
        - name: AWS_REGION
          value: "us-west-2"
    
    # 模型初始化 - 会在 model-initializer container 的 args 中添加 "--storage-uri hf://meta-llama/Llama-2-7b-hf"
    model:
      storageUri: "hf://meta-llama/Llama-2-7b-hf"
      secretRef:
        name: huggingface-token
      env:
        - name: HF_HOME
          value: "/tmp/huggingface"
  
  # 训练器配置
  trainer:
    image: pytorch/pytorch:latest
    command:
      - python
    args:
      - train.py
      - --model-path=/model
      - --dataset-path=/data
      - --epochs=3
      - --learning-rate=2e-5
    numNodes: 2
    numProcPerNode: 2
    resourcesPerNode:
      requests:
        nvidia.com/gpu: 2
        cpu: 8
        memory: 32Gi
      limits:
        nvidia.com/gpu: 2
        cpu: 16
        memory: 64Gi
    env:
      - name: NCCL_DEBUG
        value: "INFO"
  
  labels:
    experiment: "llama-fine-tuning-with-args"
    version: "v1.0"
  annotations:
    description: "LLaMA fine-tuning with StorageUri passed as container args"
