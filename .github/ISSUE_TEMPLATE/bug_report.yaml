name: Bug Report
description: Tell us about a problem you are experiencing with Kubeflow Trainer
labels: ["kind/bug", "lifecycle/needs-triage"]
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this Kubeflow Trainer bug report!
  - type: textarea
    id: problem
    attributes:
      label: What happened?
      description: |
        Please provide as much info as possible. Not doing so may result in your bug not being
        addressed in a timely manner.
    validations:
      required: true
  - type: textarea
    id: expected
    attributes:
      label: What did you expect to happen?
    validations:
      required: true
  - type: textarea
    id: environment
    attributes:
      label: Environment
      value: |
        Kubernetes version:
        ```bash
        $ kubectl version

        ```
        Kubeflow Trainer version:
        ```bash
        $ kubectl get pods -n kubeflow -l app.kubernetes.io/name=trainer -o jsonpath="{.items[*].spec.containers[*].image}"

        ```
        Kubeflow Python SDK version:
        ```bash
        $ pip show kubeflow

        ```
    validations:
      required: true
  - type: input
    id: votes
    attributes:
      label: Impacted by this bug?
      value: Give it a 👍 We prioritize the issues with most 👍
