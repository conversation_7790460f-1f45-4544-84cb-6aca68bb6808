<!--  Thanks for sending a pull request! Here are some tips for you:
1. If this is your first time, check our contributor guidelines: https://www.kubeflow.org/docs/about/contributing
2. To know more about <PERSON><PERSON><PERSON> Trainer, check the developer guide:
    https://github.com/kubeflow/trainer/blob/master/CONTRIBUTING.md
3. If you want *faster* PR reviews, check how: https://git.k8s.io/community/contributors/guide/pull-requests.md#best-practices-for-faster-reviews
-->

**What this PR does / why we need it**:

**Which issue(s) this PR fixes** _(optional, in `Fixes #<issue number>, #<issue number>, ...` format, will close the issue(s) when PR gets merged)_:
Fixes #

**Checklist:**

- [ ] [Docs](https://www.kubeflow.org/docs/components/trainer/) included if any changes are user facing
