name: Unit and Integration Test - Python

on:
  - pull_request

jobs:
  pre-commit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
      - uses: pre-commit/action@v3.0.1

  test:
    name: Test
    runs-on: ubuntu-latest
    env:
      PYTHONPATH: ${{ github.workspace }}:${PYTHONPATH}

    strategy:
      fail-fast: false
      matrix:
        # Python version to run unit and integration tests.
        python-version: ["3.11"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Run Python unit tests
        run: |
          make test-python

      - name: Run Python integration tests.
        run: |
          make test-python-integration
