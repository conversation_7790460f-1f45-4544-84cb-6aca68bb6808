# Composite action to publish Kubeflow Trainer images.
name: Build And Publish Container Images
description: Build Multiplatform Supporting Container Images

inputs:
  image:
    required: true
    description: image tag
  dockerfile:
    required: true
    description: path for Dockerfile
  platforms:
    required: true
    description: e.g, linux/amd64
  context:
    required: false
    default: .
    description: e.g, examples/xgboost/xgboost-dist
  push:
    required: true
    description: whether to push container images or not

runs:
  using: composite
  steps:
    # This step is a Workaround to avoid the "No space left on device" error.
    # ref: https://github.com/actions/runner-images/issues/2840
    - name: Remove unnecessary files
      shell: bash
      run: |
        echo "Disk usage before cleanup:"
        df -hT

        sudo rm -rf /usr/share/dotnet
        sudo rm -rf /opt/ghc
        sudo rm -rf /usr/local/share/boost
        sudo rm -rf "$AGENT_TOOLSDIRECTORY"
        sudo rm -rf /usr/local/lib/android
        sudo rm -rf /usr/local/share/powershell
        sudo rm -rf /usr/share/swift

        echo "Disk usage after cleanup:"
        df -hT

    - name: Prune docker images
      shell: bash
      run: |
        docker image prune -a -f
        docker system df
        df -hT

    - name: Move docker data directory
      shell: bash
      run: |
        echo "Stopping docker service ..."
        sudo systemctl stop docker
        DOCKER_DEFAULT_ROOT_DIR=/var/lib/docker
        DOCKER_ROOT_DIR=/mnt/docker
        echo "Moving ${DOCKER_DEFAULT_ROOT_DIR} -> ${DOCKER_ROOT_DIR}"
        sudo mv ${DOCKER_DEFAULT_ROOT_DIR} ${DOCKER_ROOT_DIR}
        echo "Creating symlink ${DOCKER_DEFAULT_ROOT_DIR} -> ${DOCKER_ROOT_DIR}"
        sudo ln -s ${DOCKER_ROOT_DIR} ${DOCKER_DEFAULT_ROOT_DIR}
        echo "$(sudo ls -l ${DOCKER_DEFAULT_ROOT_DIR})"
        echo "Starting docker service ..."
        sudo systemctl daemon-reload
        sudo systemctl start docker
        echo "Docker service status:"
        sudo systemctl --no-pager -l -o short status docker

    - name: Setup QEMU
      uses: docker/setup-qemu-action@v3
      with:
        platforms: amd64,ppc64le,arm64

    - name: Set Up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Add Docker Tags
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ inputs.image }}
        tags: |
          type=ref,event=tag
          type=raw,latest
          type=sha

    - name: Build and Push
      uses: docker/build-push-action@v5
      with:
        platforms: ${{ inputs.platforms }}
        context: ${{ inputs.context }}
        file: ${{ inputs.dockerfile }}
        push: ${{ inputs.push }}
        tags: ${{ steps.meta.outputs.tags }}
        cache-from: type=gha
        cache-to: type=gha,ignore-error=true
