name: Build and Publish Images

on:
  push:
    branches:
      - master
      - 'release-*'
    tags:
      - 'v*'
  pull_request:

jobs:
  build-and-publish:
    name: Build and Publish Images
    if: github.repository == 'kubeflow/trainer'
    runs-on:
      labels: ubuntu-latest-16-cores

    env:
      SHOULD_PUBLISH: ${{ github.event_name == 'push' }}

    strategy:
      fail-fast: false
      matrix:
        include:
          - component-name: trainer-controller-manager
            dockerfile: cmd/trainer-controller-manager/Dockerfile
            platforms: linux/amd64,linux/arm64,linux/ppc64le
          - component-name: model-initializer
            dockerfile: cmd/initializers/model/Dockerfile
            platforms: linux/amd64,linux/arm64
          - component-name: dataset-initializer
            dockerfile: cmd/initializers/dataset/Dockerfile
            platforms: linux/amd64,linux/arm64
          - component-name: deepspeed-runtime
            dockerfile: cmd/runtimes/deepspeed/Dockerfile
            platforms: linux/amd64,linux/arm64
          - component-name: mlx-runtime
            dockerfile: cmd/runtimes/mlx/Dockerfile
            platforms: linux/arm64
          - component-name: torchtune-trainer
            dockerfile: cmd/trainers/torchtune/Dockerfile
            platforms: linux/amd64,linux/arm64
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: GHCR Login
        if: env.SHOULD_PUBLISH == 'true'
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Docker Hub Login
        if: env.SHOULD_PUBLISH == 'true'
        uses: docker/login-action@v3
        with:
          registry: docker.io
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Publish Component ${{ matrix.component-name }}
        if: env.SHOULD_PUBLISH == 'true'
        id: publish
        uses: ./.github/workflows/template-publish-image
        with:
          image: |
            ghcr.io/kubeflow/trainer/${{ matrix.component-name }}
            docker.io/kubeflow/${{ matrix.component-name }}
          dockerfile: ${{ matrix.dockerfile }}
          platforms: ${{ matrix.platforms }}
          context: ${{ matrix.context }}
          push: true

      - name: Test Build For Component ${{ matrix.component-name }}
        if: env.SHOULD_PUBLISH != 'true'
        uses: ./.github/workflows/template-publish-image
        with:
          image: |
            ghcr.io/kubeflow/trainer/${{ matrix.component-name }}
            docker.io/kubeflow/${{ matrix.component-name }}
          dockerfile: ${{ matrix.dockerfile }}
          platforms: ${{ matrix.platforms }}
          context: ${{ matrix.context }}
          push: false
