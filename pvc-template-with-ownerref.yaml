# PVC 模板 - 使用 OwnerReference 绑定到 TrainJob
# 这个 PVC 会在 TrainJob 删除时自动删除
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: "{{ .TrainJobName }}-workspace"  # 模板变量，实际使用时需要替换
  namespace: "{{ .TrainJobNamespace }}"
  labels:
    app: trainer
    trainjob: "{{ .TrainJobName }}"
    component: shared-workspace
  ownerReferences:
    - apiVersion: trainer.kubeflow.org/v1alpha1
      kind: TrainJob
      name: "{{ .TrainJobName }}"
      uid: "{{ .TrainJobUID }}"
      controller: true
      blockOwnerDeletion: true
spec:
  accessModes:
    - ReadWriteMany  # 支持多个 Pod 同时访问
  resources:
    requests:
      storage: 500Gi  # 默认大小，可以通过 TrainJob 配置覆盖
  storageClassName: "{{ .StorageClassName | default "default" }}"  # 可配置的存储类

---
# 简化的 TrainJob 示例 - 使用自动创建的 PVC
apiVersion: trainer.kubeflow.org/v1alpha1
kind: TrainJob
metadata:
  name: llm-sft-auto-pvc
  namespace: default
  annotations:
    # 自定义注解来配置 PVC
    trainer.kubeflow.org/pvc-size: "500Gi"
    trainer.kubeflow.org/storage-class: "nfs-storage"
    trainer.kubeflow.org/auto-create-pvc: "true"
spec:
  runtimeRef:
    name: sft-with-pvc
    apiGroup: trainer.kubeflow.org
    kind: ClusterTrainingRuntime
  
  initializer:
    dataset:
      storageUri: "cc://root/alpaca-gpt4-data-zh"
      secretRef: 
        name: cetccloud-credentials
    model:
      storageUri: "cc://root/Qwen2.5-7B-Instruct"
      secretRef:
        name: cetccloud-credentials
  
  trainer:
    image: 172.28.0.32:3443/jdcloud/swift:ubuntu22.04-cuda12.4.0-py311-torch2.6.0-vllm0.8.5.post1-modelscope1.26.0-swift3.4.1.post1
    command: ["swift"]
    args:
      - sft
      - --model_type=qwen2_5-7b-instruct
      - --model_id_or_path=/workspace/model
      - --dataset_dir=/workspace/data
      - --output_dir=/workspace/output
      - --num_train_epochs=1
    numNodes: 2
    numProcPerNode: 2
    resourcesPerNode:
      requests:
        nvidia.com/gpu: 2
        cpu: 8
        memory: 32Gi
  
  # 自动配置 PVC - 这部分会由 controller 自动处理
  podSpecOverrides:
    - targetJobs:
        - name: dataset-initializer
        - name: model-initializer
        - name: node
      volumes:
        - name: shared-workspace
          persistentVolumeClaim:
            claimName: "llm-sft-auto-pvc-workspace"  # 自动生成的 PVC 名称
