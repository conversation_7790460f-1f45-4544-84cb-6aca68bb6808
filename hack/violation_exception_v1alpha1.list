API rule violation: list_type_missing,sigs.k8s.io/jobset/api/jobset/v1alpha2,FailurePolicy,Rules
API rule violation: list_type_missing,sigs.k8s.io/jobset/api/jobset/v1alpha2,FailurePolicyRule,OnJobFailureReasons
API rule violation: names_match,k8s.io/api/core/v1,AzureDiskVolumeSource,DataDiskURI
API rule violation: names_match,k8s.io/api/core/v1,ContainerStatus,LastTerminationState
API rule violation: names_match,k8s.io/api/core/v1,DaemonEndpoint,Port
API rule violation: names_match,k8s.io/api/core/v1,Event,ReportingController
API rule violation: names_match,k8s.io/api/core/v1,FCVolumeSource,WWIDs
API rule violation: names_match,k8s.io/api/core/v1,GlusterfsPersistentVolumeSource,EndpointsName
API rule violation: names_match,k8s.io/api/core/v1,GlusterfsVolumeSource,EndpointsName
API rule violation: names_match,k8s.io/api/core/v1,ISCSIPersistentVolumeSource,DiscoveryCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,ISCSIPersistentVolumeSource,SessionCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,ISCSIVolumeSource,DiscoveryCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,ISCSIVolumeSource,SessionCHAPAuth
API rule violation: names_match,k8s.io/api/core/v1,NodeSpec,DoNotUseExternalID
API rule violation: names_match,k8s.io/api/core/v1,PersistentVolumeSource,CephFS
API rule violation: names_match,k8s.io/api/core/v1,PersistentVolumeSource,StorageOS
API rule violation: names_match,k8s.io/api/core/v1,PodSpec,DeprecatedServiceAccount
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,CephMonitors
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,RBDImage
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,RBDPool
API rule violation: names_match,k8s.io/api/core/v1,RBDPersistentVolumeSource,RadosUser
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,CephMonitors
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,RBDImage
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,RBDPool
API rule violation: names_match,k8s.io/api/core/v1,RBDVolumeSource,RadosUser
API rule violation: names_match,k8s.io/api/core/v1,VolumeSource,CephFS
API rule violation: names_match,k8s.io/api/core/v1,VolumeSource,StorageOS
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,Format
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,d
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,i
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,Quantity,s
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,int64Amount,scale
API rule violation: names_match,k8s.io/apimachinery/pkg/api/resource,int64Amount,value
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,APIResourceList,APIResources
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,Duration,Duration
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,InternalEvent,Object
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,InternalEvent,Type
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,MicroTime,Time
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,StatusCause,Type
API rule violation: names_match,k8s.io/apimachinery/pkg/apis/meta/v1,Time,Time
API rule violation: names_match,k8s.io/apimachinery/pkg/runtime,Unknown,ContentEncoding
API rule violation: names_match,k8s.io/apimachinery/pkg/runtime,Unknown,ContentType
API rule violation: names_match,k8s.io/apimachinery/pkg/util/intstr,IntOrString,IntVal
API rule violation: names_match,k8s.io/apimachinery/pkg/util/intstr,IntOrString,StrVal
API rule violation: names_match,k8s.io/apimachinery/pkg/util/intstr,IntOrString,Type
