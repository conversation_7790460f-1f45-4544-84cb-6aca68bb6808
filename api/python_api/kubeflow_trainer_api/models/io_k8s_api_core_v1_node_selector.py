# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List
from kubeflow_trainer_api.models.io_k8s_api_core_v1_node_selector_term import IoK8sApiCoreV1NodeSelectorTerm
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1NodeSelector(BaseModel):
    """
    A node selector represents the union of the results of one or more label queries over a set of nodes; that is, it represents the OR of the selectors represented by the node selector terms.
    """ # noqa: E501
    node_selector_terms: List[IoK8sApiCoreV1NodeSelectorTerm] = Field(description="Required. A list of node selector terms. The terms are ORed.", alias="nodeSelectorTerms")
    __properties: ClassVar[List[str]] = ["nodeSelectorTerms"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1NodeSelector from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in node_selector_terms (list)
        _items = []
        if self.node_selector_terms:
            for _item_node_selector_terms in self.node_selector_terms:
                if _item_node_selector_terms:
                    _items.append(_item_node_selector_terms.to_dict())
            _dict['nodeSelectorTerms'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1NodeSelector from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "nodeSelectorTerms": [IoK8sApiCoreV1NodeSelectorTerm.from_dict(_item) for _item in obj["nodeSelectorTerms"]] if obj.get("nodeSelectorTerms") is not None else None
        })
        return _obj


