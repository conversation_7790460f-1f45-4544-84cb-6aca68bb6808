# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_exec_action import IoK8sApiCoreV1ExecAction
from kubeflow_trainer_api.models.io_k8s_api_core_v1_http_get_action import IoK8sApiCoreV1HTTPGetAction
from kubeflow_trainer_api.models.io_k8s_api_core_v1_sleep_action import IoK8sApiCoreV1SleepAction
from kubeflow_trainer_api.models.io_k8s_api_core_v1_tcp_socket_action import IoK8sApi<PERSON>oreV1TCPSocketAction
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1LifecycleHandler(BaseModel):
    """
    LifecycleHandler defines a specific action that should be taken in a lifecycle hook. One and only one of the fields, except TCPSocket must be specified.
    """ # noqa: E501
    var_exec: Optional[IoK8sApiCoreV1ExecAction] = Field(default=None, description="Exec specifies a command to execute in the container.", alias="exec")
    http_get: Optional[IoK8sApiCoreV1HTTPGetAction] = Field(default=None, description="HTTPGet specifies an HTTP GET request to perform.", alias="httpGet")
    sleep: Optional[IoK8sApiCoreV1SleepAction] = Field(default=None, description="Sleep represents a duration that the container should sleep.")
    tcp_socket: Optional[IoK8sApiCoreV1TCPSocketAction] = Field(default=None, description="Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept for backward compatibility. There is no validation of this field and lifecycle hooks will fail at runtime when it is specified.", alias="tcpSocket")
    __properties: ClassVar[List[str]] = ["exec", "httpGet", "sleep", "tcpSocket"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1LifecycleHandler from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of var_exec
        if self.var_exec:
            _dict['exec'] = self.var_exec.to_dict()
        # override the default output from pydantic by calling `to_dict()` of http_get
        if self.http_get:
            _dict['httpGet'] = self.http_get.to_dict()
        # override the default output from pydantic by calling `to_dict()` of sleep
        if self.sleep:
            _dict['sleep'] = self.sleep.to_dict()
        # override the default output from pydantic by calling `to_dict()` of tcp_socket
        if self.tcp_socket:
            _dict['tcpSocket'] = self.tcp_socket.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1LifecycleHandler from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "exec": IoK8sApiCoreV1ExecAction.from_dict(obj["exec"]) if obj.get("exec") is not None else None,
            "httpGet": IoK8sApiCoreV1HTTPGetAction.from_dict(obj["httpGet"]) if obj.get("httpGet") is not None else None,
            "sleep": IoK8sApiCoreV1SleepAction.from_dict(obj["sleep"]) if obj.get("sleep") is not None else None,
            "tcpSocket": IoK8sApiCoreV1TCPSocketAction.from_dict(obj["tcpSocket"]) if obj.get("tcpSocket") is not None else None
        })
        return _obj


