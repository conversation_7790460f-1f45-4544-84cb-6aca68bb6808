# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_autoscaling_v2_hpa_scaling_rules import IoK8sApiAutoscalingV2HPAScalingRules
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiAutoscalingV2HorizontalPodAutoscalerBehavior(BaseModel):
    """
    HorizontalPodAutoscalerBehavior configures the scaling behavior of the target in both Up and Down directions (scaleUp and scaleDown fields respectively).
    """ # noqa: E501
    scale_down: Optional[IoK8sApiAutoscalingV2HPAScalingRules] = Field(default=None, description="scaleDown is scaling policy for scaling Down. If not set, the default value is to allow to scale down to minReplicas pods, with a 300 second stabilization window (i.e., the highest recommendation for the last 300sec is used).", alias="scaleDown")
    scale_up: Optional[IoK8sApiAutoscalingV2HPAScalingRules] = Field(default=None, description="scaleUp is scaling policy for scaling Up. If not set, the default value is the higher of:   * increase no more than 4 pods per 60 seconds   * double the number of pods per 60 seconds No stabilization is used.", alias="scaleUp")
    __properties: ClassVar[List[str]] = ["scaleDown", "scaleUp"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiAutoscalingV2HorizontalPodAutoscalerBehavior from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of scale_down
        if self.scale_down:
            _dict['scaleDown'] = self.scale_down.to_dict()
        # override the default output from pydantic by calling `to_dict()` of scale_up
        if self.scale_up:
            _dict['scaleUp'] = self.scale_up.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiAutoscalingV2HorizontalPodAutoscalerBehavior from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "scaleDown": IoK8sApiAutoscalingV2HPAScalingRules.from_dict(obj["scaleDown"]) if obj.get("scaleDown") is not None else None,
            "scaleUp": IoK8sApiAutoscalingV2HPAScalingRules.from_dict(obj["scaleUp"]) if obj.get("scaleUp") is not None else None
        })
        return _obj


