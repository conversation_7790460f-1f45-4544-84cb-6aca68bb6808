# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.jobset_v1alpha2_coordinator import JobsetV1alpha2Coordinator
from kubeflow_trainer_api.models.jobset_v1alpha2_failure_policy import JobsetV1alpha2FailurePolicy
from kubeflow_trainer_api.models.jobset_v1alpha2_network import JobsetV1alpha2Network
from kubeflow_trainer_api.models.jobset_v1alpha2_replicated_job import JobsetV1alpha2Replicated<PERSON>ob
from kubeflow_trainer_api.models.jobset_v1alpha2_startup_policy import JobsetV1alpha2StartupPolicy
from kubeflow_trainer_api.models.jobset_v1alpha2_success_policy import JobsetV1alpha2SuccessPolicy
from typing import Optional, Set
from typing_extensions import Self

class JobsetV1alpha2JobSetSpec(BaseModel):
    """
    JobSetSpec defines the desired state of JobSet
    """ # noqa: E501
    coordinator: Optional[JobsetV1alpha2Coordinator] = Field(default=None, description="Coordinator can be used to assign a specific pod as the coordinator for the JobSet. If defined, an annotation will be added to all Jobs and pods with coordinator pod, which contains the stable network endpoint where the coordinator pod can be reached. jobset.sigs.k8s.io/coordinator=<pod hostname>.<headless service>")
    failure_policy: Optional[JobsetV1alpha2FailurePolicy] = Field(default=None, description="FailurePolicy, if set, configures when to declare the JobSet as failed. The JobSet is always declared failed if any job in the set finished with status failed.", alias="failurePolicy")
    managed_by: Optional[StrictStr] = Field(default=None, description="ManagedBy is used to indicate the controller or entity that manages a JobSet. The built-in JobSet controller reconciles JobSets which don't have this field at all or the field value is the reserved string `jobset.sigs.k8s.io/jobset-controller`, but skips reconciling JobSets with a custom value for this field.  The value must be a valid domain-prefixed path (e.g. acme.io/foo) - all characters before the first \"/\" must be a valid subdomain as defined by RFC 1123. All characters trailing the first \"/\" must be valid HTTP Path characters as defined by RFC 3986. The value cannot exceed 63 characters. The field is immutable.", alias="managedBy")
    network: Optional[JobsetV1alpha2Network] = Field(default=None, description="Network defines the networking options for the jobset.")
    replicated_jobs: Optional[List[JobsetV1alpha2ReplicatedJob]] = Field(default=None, description="ReplicatedJobs is the group of jobs that will form the set.", alias="replicatedJobs")
    startup_policy: Optional[JobsetV1alpha2StartupPolicy] = Field(default=None, description="StartupPolicy, if set, configures in what order jobs must be started Deprecated: StartupPolicy is deprecated, please use the DependsOn API.", alias="startupPolicy")
    success_policy: Optional[JobsetV1alpha2SuccessPolicy] = Field(default=None, description="SuccessPolicy configures when to declare the JobSet as succeeded. The JobSet is always declared succeeded if all jobs in the set finished with status complete.", alias="successPolicy")
    suspend: Optional[StrictBool] = Field(default=None, description="Suspend suspends all running child Jobs when set to true.")
    ttl_seconds_after_finished: Optional[StrictInt] = Field(default=None, description="TTLSecondsAfterFinished limits the lifetime of a JobSet that has finished execution (either Complete or Failed). If this field is set, TTLSecondsAfterFinished after the JobSet finishes, it is eligible to be automatically deleted. When the JobSet is being deleted, its lifecycle guarantees (e.g. finalizers) will be honored. If this field is unset, the JobSet won't be automatically deleted. If this field is set to zero, the JobSet becomes eligible to be deleted immediately after it finishes.", alias="ttlSecondsAfterFinished")
    __properties: ClassVar[List[str]] = ["coordinator", "failurePolicy", "managedBy", "network", "replicatedJobs", "startupPolicy", "successPolicy", "suspend", "ttlSecondsAfterFinished"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of JobsetV1alpha2JobSetSpec from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of coordinator
        if self.coordinator:
            _dict['coordinator'] = self.coordinator.to_dict()
        # override the default output from pydantic by calling `to_dict()` of failure_policy
        if self.failure_policy:
            _dict['failurePolicy'] = self.failure_policy.to_dict()
        # override the default output from pydantic by calling `to_dict()` of network
        if self.network:
            _dict['network'] = self.network.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in replicated_jobs (list)
        _items = []
        if self.replicated_jobs:
            for _item_replicated_jobs in self.replicated_jobs:
                if _item_replicated_jobs:
                    _items.append(_item_replicated_jobs.to_dict())
            _dict['replicatedJobs'] = _items
        # override the default output from pydantic by calling `to_dict()` of startup_policy
        if self.startup_policy:
            _dict['startupPolicy'] = self.startup_policy.to_dict()
        # override the default output from pydantic by calling `to_dict()` of success_policy
        if self.success_policy:
            _dict['successPolicy'] = self.success_policy.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of JobsetV1alpha2JobSetSpec from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "coordinator": JobsetV1alpha2Coordinator.from_dict(obj["coordinator"]) if obj.get("coordinator") is not None else None,
            "failurePolicy": JobsetV1alpha2FailurePolicy.from_dict(obj["failurePolicy"]) if obj.get("failurePolicy") is not None else None,
            "managedBy": obj.get("managedBy"),
            "network": JobsetV1alpha2Network.from_dict(obj["network"]) if obj.get("network") is not None else None,
            "replicatedJobs": [JobsetV1alpha2ReplicatedJob.from_dict(_item) for _item in obj["replicatedJobs"]] if obj.get("replicatedJobs") is not None else None,
            "startupPolicy": JobsetV1alpha2StartupPolicy.from_dict(obj["startupPolicy"]) if obj.get("startupPolicy") is not None else None,
            "successPolicy": JobsetV1alpha2SuccessPolicy.from_dict(obj["successPolicy"]) if obj.get("successPolicy") is not None else None,
            "suspend": obj.get("suspend"),
            "ttlSecondsAfterFinished": obj.get("ttlSecondsAfterFinished")
        })
        return _obj


