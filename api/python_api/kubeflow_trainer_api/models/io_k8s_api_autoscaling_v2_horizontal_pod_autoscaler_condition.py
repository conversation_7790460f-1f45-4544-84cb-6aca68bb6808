# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiAutoscalingV2HorizontalPodAutoscalerCondition(BaseModel):
    """
    HorizontalPodAutoscalerCondition describes the state of a HorizontalPodAutoscaler at a certain point.
    """ # noqa: E501
    last_transition_time: Optional[datetime] = Field(default=None, description="lastTransitionTime is the last time the condition transitioned from one status to another", alias="lastTransitionTime")
    message: Optional[StrictStr] = Field(default=None, description="message is a human-readable explanation containing details about the transition")
    reason: Optional[StrictStr] = Field(default=None, description="reason is the reason for the condition's last transition.")
    status: StrictStr = Field(description="status is the status of the condition (True, False, Unknown)")
    type: StrictStr = Field(description="type describes the current condition")
    __properties: ClassVar[List[str]] = ["lastTransitionTime", "message", "reason", "status", "type"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiAutoscalingV2HorizontalPodAutoscalerCondition from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiAutoscalingV2HorizontalPodAutoscalerCondition from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "lastTransitionTime": obj.get("lastTransitionTime"),
            "message": obj.get("message"),
            "reason": obj.get("reason"),
            "status": obj.get("status") if obj.get("status") is not None else '',
            "type": obj.get("type") if obj.get("type") is not None else ''
        })
        return _obj


