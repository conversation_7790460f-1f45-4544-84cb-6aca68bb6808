# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1AzureDiskVolumeSource(BaseModel):
    """
    AzureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.
    """ # noqa: E501
    caching_mode: Optional[StrictStr] = Field(default='ReadWrite', description="cachingMode is the Host Caching mode: None, Read Only, Read Write.  Possible enum values:  - `\"None\"`  - `\"ReadOnly\"`  - `\"ReadWrite\"`", alias="cachingMode")
    disk_name: StrictStr = Field(description="diskName is the Name of the data disk in the blob storage", alias="diskName")
    disk_uri: StrictStr = Field(description="diskURI is the URI of data disk in the blob storage", alias="diskURI")
    fs_type: Optional[StrictStr] = Field(default='ext4', description="fsType is Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.", alias="fsType")
    kind: Optional[StrictStr] = Field(default='Shared', description="kind expected values are Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared  Possible enum values:  - `\"Dedicated\"`  - `\"Managed\"`  - `\"Shared\"`")
    read_only: Optional[StrictBool] = Field(default=False, description="readOnly Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", alias="readOnly")
    __properties: ClassVar[List[str]] = ["cachingMode", "diskName", "diskURI", "fsType", "kind", "readOnly"]

    @field_validator('caching_mode')
    def caching_mode_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['None', 'ReadOnly', 'ReadWrite']):
            raise ValueError("must be one of enum values ('None', 'ReadOnly', 'ReadWrite')")
        return value

    @field_validator('kind')
    def kind_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['Dedicated', 'Managed', 'Shared']):
            raise ValueError("must be one of enum values ('Dedicated', 'Managed', 'Shared')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1AzureDiskVolumeSource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1AzureDiskVolumeSource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "cachingMode": obj.get("cachingMode") if obj.get("cachingMode") is not None else 'ReadWrite',
            "diskName": obj.get("diskName") if obj.get("diskName") is not None else '',
            "diskURI": obj.get("diskURI") if obj.get("diskURI") is not None else '',
            "fsType": obj.get("fsType") if obj.get("fsType") is not None else 'ext4',
            "kind": obj.get("kind") if obj.get("kind") is not None else 'Shared',
            "readOnly": obj.get("readOnly") if obj.get("readOnly") is not None else False
        })
        return _obj


