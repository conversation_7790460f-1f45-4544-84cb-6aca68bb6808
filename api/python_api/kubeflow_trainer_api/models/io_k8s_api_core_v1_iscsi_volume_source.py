# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_local_object_reference import IoK8sApiCoreV1LocalObjectReference
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1ISCSIVolumeSource(BaseModel):
    """
    Represents an ISCSI disk. ISCSI volumes can only be mounted as read/write once. ISCSI volumes support ownership management and SELinux relabeling.
    """ # noqa: E501
    chap_auth_discovery: Optional[StrictBool] = Field(default=None, description="chapAuthDiscovery defines whether support iSCSI Discovery CHAP authentication", alias="chapAuthDiscovery")
    chap_auth_session: Optional[StrictBool] = Field(default=None, description="chapAuthSession defines whether support iSCSI Session CHAP authentication", alias="chapAuthSession")
    fs_type: Optional[StrictStr] = Field(default=None, description="fsType is the filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi", alias="fsType")
    initiator_name: Optional[StrictStr] = Field(default=None, description="initiatorName is the custom iSCSI Initiator Name. If initiatorName is specified with iscsiInterface simultaneously, new iSCSI interface <target portal>:<volume name> will be created for the connection.", alias="initiatorName")
    iqn: StrictStr = Field(description="iqn is the target iSCSI Qualified Name.")
    iscsi_interface: Optional[StrictStr] = Field(default='default', description="iscsiInterface is the interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).", alias="iscsiInterface")
    lun: StrictInt = Field(description="lun represents iSCSI Target Lun number.")
    portals: Optional[List[StrictStr]] = Field(default=None, description="portals is the iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).")
    read_only: Optional[StrictBool] = Field(default=None, description="readOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.", alias="readOnly")
    secret_ref: Optional[IoK8sApiCoreV1LocalObjectReference] = Field(default=None, description="secretRef is the CHAP Secret for iSCSI target and initiator authentication", alias="secretRef")
    target_portal: StrictStr = Field(description="targetPortal is iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).", alias="targetPortal")
    __properties: ClassVar[List[str]] = ["chapAuthDiscovery", "chapAuthSession", "fsType", "initiatorName", "iqn", "iscsiInterface", "lun", "portals", "readOnly", "secretRef", "targetPortal"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ISCSIVolumeSource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of secret_ref
        if self.secret_ref:
            _dict['secretRef'] = self.secret_ref.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ISCSIVolumeSource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "chapAuthDiscovery": obj.get("chapAuthDiscovery"),
            "chapAuthSession": obj.get("chapAuthSession"),
            "fsType": obj.get("fsType"),
            "initiatorName": obj.get("initiatorName"),
            "iqn": obj.get("iqn") if obj.get("iqn") is not None else '',
            "iscsiInterface": obj.get("iscsiInterface") if obj.get("iscsiInterface") is not None else 'default',
            "lun": obj.get("lun") if obj.get("lun") is not None else 0,
            "portals": obj.get("portals"),
            "readOnly": obj.get("readOnly"),
            "secretRef": IoK8sApiCoreV1LocalObjectReference.from_dict(obj["secretRef"]) if obj.get("secretRef") is not None else None,
            "targetPortal": obj.get("targetPortal") if obj.get("targetPortal") is not None else ''
        })
        return _obj


