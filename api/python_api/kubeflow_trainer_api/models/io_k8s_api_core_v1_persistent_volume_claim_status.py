# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_modify_volume_status import IoK8sApiCoreV1ModifyVolumeStatus
from kubeflow_trainer_api.models.io_k8s_api_core_v1_persistent_volume_claim_condition import IoK8sApiCoreV1PersistentVolumeClaimCondition
from kubeflow_trainer_api.models.io_k8s_apimachinery_pkg_api_resource_quantity import IoK8sApimachineryPkgApiResourceQuantity
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1PersistentVolumeClaimStatus(BaseModel):
    """
    PersistentVolumeClaimStatus is the current status of a persistent volume claim.
    """ # noqa: E501
    access_modes: Optional[List[StrictStr]] = Field(default=None, description="accessModes contains the actual access modes the volume backing the PVC has. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1", alias="accessModes")
    allocated_resource_statuses: Optional[Dict[str, StrictStr]] = Field(default=None, description="allocatedResourceStatuses stores status of resource being resized for the given PVC. Key names follow standard Kubernetes label syntax. Valid values are either:  * Un-prefixed keys:   - storage - the capacity of the volume.  * Custom resources must use implementation-defined prefixed names such as \"example.com/my-custom-resource\" Apart from above values - keys that are unprefixed or have kubernetes.io prefix are considered reserved and hence may not be used.  ClaimResourceStatus can be in any of following states:  - ControllerResizeInProgress:   State set when resize controller starts resizing the volume in control-plane.  - ControllerResizeFailed:   State set when resize has failed in resize controller with a terminal error.  - NodeResizePending:   State set when resize controller has finished resizing the volume but further resizing of   volume is needed on the node.  - NodeResizeInProgress:   State set when kubelet starts resizing the volume.  - NodeResizeFailed:   State set when resizing has failed in kubelet with a terminal error. Transient errors don't set   NodeResizeFailed. For example: if expanding a PVC for more capacity - this field can be one of the following states:  - pvc.status.allocatedResourceStatus['storage'] = \"ControllerResizeInProgress\"      - pvc.status.allocatedResourceStatus['storage'] = \"ControllerResizeFailed\"      - pvc.status.allocatedResourceStatus['storage'] = \"NodeResizePending\"      - pvc.status.allocatedResourceStatus['storage'] = \"NodeResizeInProgress\"      - pvc.status.allocatedResourceStatus['storage'] = \"NodeResizeFailed\" When this field is not set, it means that no resize operation is in progress for the given PVC.  A controller that receives PVC update with previously unknown resourceName or ClaimResourceStatus should ignore the update for the purpose it was designed. For example - a controller that only is responsible for resizing capacity of the volume, should ignore PVC updates that change other valid resources associated with PVC.  This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.", alias="allocatedResourceStatuses")
    allocated_resources: Optional[Dict[str, IoK8sApimachineryPkgApiResourceQuantity]] = Field(default=None, description="allocatedResources tracks the resources allocated to a PVC including its capacity. Key names follow standard Kubernetes label syntax. Valid values are either:  * Un-prefixed keys:   - storage - the capacity of the volume.  * Custom resources must use implementation-defined prefixed names such as \"example.com/my-custom-resource\" Apart from above values - keys that are unprefixed or have kubernetes.io prefix are considered reserved and hence may not be used.  Capacity reported here may be larger than the actual capacity when a volume expansion operation is requested. For storage quota, the larger value from allocatedResources and PVC.spec.resources is used. If allocatedResources is not set, PVC.spec.resources alone is used for quota calculation. If a volume expansion capacity request is lowered, allocatedResources is only lowered if there are no expansion operations in progress and if the actual volume capacity is equal or lower than the requested capacity.  A controller that receives PVC update with previously unknown resourceName should ignore the update for the purpose it was designed. For example - a controller that only is responsible for resizing capacity of the volume, should ignore PVC updates that change other valid resources associated with PVC.  This is an alpha field and requires enabling RecoverVolumeExpansionFailure feature.", alias="allocatedResources")
    capacity: Optional[Dict[str, IoK8sApimachineryPkgApiResourceQuantity]] = Field(default=None, description="capacity represents the actual resources of the underlying volume.")
    conditions: Optional[List[IoK8sApiCoreV1PersistentVolumeClaimCondition]] = Field(default=None, description="conditions is the current Condition of persistent volume claim. If underlying persistent volume is being resized then the Condition will be set to 'Resizing'.")
    current_volume_attributes_class_name: Optional[StrictStr] = Field(default=None, description="currentVolumeAttributesClassName is the current name of the VolumeAttributesClass the PVC is using. When unset, there is no VolumeAttributeClass applied to this PersistentVolumeClaim This is a beta field and requires enabling VolumeAttributesClass feature (off by default).", alias="currentVolumeAttributesClassName")
    modify_volume_status: Optional[IoK8sApiCoreV1ModifyVolumeStatus] = Field(default=None, description="ModifyVolumeStatus represents the status object of ControllerModifyVolume operation. When this is unset, there is no ModifyVolume operation being attempted. This is a beta field and requires enabling VolumeAttributesClass feature (off by default).", alias="modifyVolumeStatus")
    phase: Optional[StrictStr] = Field(default=None, description="phase represents the current phase of PersistentVolumeClaim.  Possible enum values:  - `\"Bound\"` used for PersistentVolumeClaims that are bound  - `\"Lost\"` used for PersistentVolumeClaims that lost their underlying PersistentVolume. The claim was bound to a PersistentVolume and this volume does not exist any longer and all data on it was lost.  - `\"Pending\"` used for PersistentVolumeClaims that are not yet bound")
    __properties: ClassVar[List[str]] = ["accessModes", "allocatedResourceStatuses", "allocatedResources", "capacity", "conditions", "currentVolumeAttributesClassName", "modifyVolumeStatus", "phase"]

    @field_validator('access_modes')
    def access_modes_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        for i in value:
            if i not in set(['ReadOnlyMany', 'ReadWriteMany', 'ReadWriteOnce', 'ReadWriteOncePod']):
                raise ValueError("each list item must be one of ('ReadOnlyMany', 'ReadWriteMany', 'ReadWriteOnce', 'ReadWriteOncePod')")
        return value

    @field_validator('allocated_resource_statuses')
    def allocated_resource_statuses_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        for i in value.values():
            if i not in set(['ControllerResizeInProgress', 'ControllerResizeInfeasible', 'NodeResizeInProgress', 'NodeResizeInfeasible', 'NodeResizePending']):
                raise ValueError("dict values must be one of enum values ('ControllerResizeInProgress', 'ControllerResizeInfeasible', 'NodeResizeInProgress', 'NodeResizeInfeasible', 'NodeResizePending')")
        return value

    @field_validator('phase')
    def phase_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['Bound', 'Lost', 'Pending']):
            raise ValueError("must be one of enum values ('Bound', 'Lost', 'Pending')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1PersistentVolumeClaimStatus from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each value in allocated_resources (dict)
        _field_dict = {}
        if self.allocated_resources:
            for _key_allocated_resources in self.allocated_resources:
                if self.allocated_resources[_key_allocated_resources]:
                    _field_dict[_key_allocated_resources] = self.allocated_resources[_key_allocated_resources].to_dict()
            _dict['allocatedResources'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of each value in capacity (dict)
        _field_dict = {}
        if self.capacity:
            for _key_capacity in self.capacity:
                if self.capacity[_key_capacity]:
                    _field_dict[_key_capacity] = self.capacity[_key_capacity].to_dict()
            _dict['capacity'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of each item in conditions (list)
        _items = []
        if self.conditions:
            for _item_conditions in self.conditions:
                if _item_conditions:
                    _items.append(_item_conditions.to_dict())
            _dict['conditions'] = _items
        # override the default output from pydantic by calling `to_dict()` of modify_volume_status
        if self.modify_volume_status:
            _dict['modifyVolumeStatus'] = self.modify_volume_status.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1PersistentVolumeClaimStatus from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "accessModes": obj.get("accessModes"),
            "allocatedResourceStatuses": obj.get("allocatedResourceStatuses"),
            "allocatedResources": dict(
                (_k, IoK8sApimachineryPkgApiResourceQuantity.from_dict(_v))
                for _k, _v in obj["allocatedResources"].items()
            )
            if obj.get("allocatedResources") is not None
            else None,
            "capacity": dict(
                (_k, IoK8sApimachineryPkgApiResourceQuantity.from_dict(_v))
                for _k, _v in obj["capacity"].items()
            )
            if obj.get("capacity") is not None
            else None,
            "conditions": [IoK8sApiCoreV1PersistentVolumeClaimCondition.from_dict(_item) for _item in obj["conditions"]] if obj.get("conditions") is not None else None,
            "currentVolumeAttributesClassName": obj.get("currentVolumeAttributesClassName"),
            "modifyVolumeStatus": IoK8sApiCoreV1ModifyVolumeStatus.from_dict(obj["modifyVolumeStatus"]) if obj.get("modifyVolumeStatus") is not None else None,
            "phase": obj.get("phase")
        })
        return _obj


