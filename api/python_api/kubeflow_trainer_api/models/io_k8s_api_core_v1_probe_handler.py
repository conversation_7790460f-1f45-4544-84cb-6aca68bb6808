# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_exec_action import IoK8sApiCoreV1ExecAction
from kubeflow_trainer_api.models.io_k8s_api_core_v1_grpc_action import IoK8sApiCoreV1GRPCAction
from kubeflow_trainer_api.models.io_k8s_api_core_v1_http_get_action import IoK8sApiCoreV1HTTPGetAction
from kubeflow_trainer_api.models.io_k8s_api_core_v1_tcp_socket_action import IoK8sApiCoreV1TCPSocketAction
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1ProbeHandler(BaseModel):
    """
    ProbeHandler defines a specific action that should be taken in a probe. One and only one of the fields must be specified.
    """ # noqa: E501
    var_exec: Optional[IoK8sApiCoreV1ExecAction] = Field(default=None, description="Exec specifies a command to execute in the container.", alias="exec")
    grpc: Optional[IoK8sApiCoreV1GRPCAction] = Field(default=None, description="GRPC specifies a GRPC HealthCheckRequest.")
    http_get: Optional[IoK8sApiCoreV1HTTPGetAction] = Field(default=None, description="HTTPGet specifies an HTTP GET request to perform.", alias="httpGet")
    tcp_socket: Optional[IoK8sApiCoreV1TCPSocketAction] = Field(default=None, description="TCPSocket specifies a connection to a TCP port.", alias="tcpSocket")
    __properties: ClassVar[List[str]] = ["exec", "grpc", "httpGet", "tcpSocket"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ProbeHandler from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of var_exec
        if self.var_exec:
            _dict['exec'] = self.var_exec.to_dict()
        # override the default output from pydantic by calling `to_dict()` of grpc
        if self.grpc:
            _dict['grpc'] = self.grpc.to_dict()
        # override the default output from pydantic by calling `to_dict()` of http_get
        if self.http_get:
            _dict['httpGet'] = self.http_get.to_dict()
        # override the default output from pydantic by calling `to_dict()` of tcp_socket
        if self.tcp_socket:
            _dict['tcpSocket'] = self.tcp_socket.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ProbeHandler from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "exec": IoK8sApiCoreV1ExecAction.from_dict(obj["exec"]) if obj.get("exec") is not None else None,
            "grpc": IoK8sApiCoreV1GRPCAction.from_dict(obj["grpc"]) if obj.get("grpc") is not None else None,
            "httpGet": IoK8sApiCoreV1HTTPGetAction.from_dict(obj["httpGet"]) if obj.get("httpGet") is not None else None,
            "tcpSocket": IoK8sApiCoreV1TCPSocketAction.from_dict(obj["tcpSocket"]) if obj.get("tcpSocket") is not None else None
        })
        return _obj


