# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_app_armor_profile import IoK8sApiCoreV1AppArmorProfile
from kubeflow_trainer_api.models.io_k8s_api_core_v1_se_linux_options import IoK8sApiCoreV1SELinuxOptions
from kubeflow_trainer_api.models.io_k8s_api_core_v1_seccomp_profile import IoK8sApiCoreV1SeccompProfile
from kubeflow_trainer_api.models.io_k8s_api_core_v1_sysctl import IoK8sApiCoreV1Sysctl
from kubeflow_trainer_api.models.io_k8s_api_core_v1_windows_security_context_options import IoK8sApiCoreV1WindowsSecurityContextOptions
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1PodSecurityContext(BaseModel):
    """
    PodSecurityContext holds pod-level security attributes and common container settings. Some fields are also present in container.securityContext.  Field values of container.securityContext take precedence over field values of PodSecurityContext.
    """ # noqa: E501
    app_armor_profile: Optional[IoK8sApiCoreV1AppArmorProfile] = Field(default=None, description="appArmorProfile is the AppArmor options to use by the containers in this pod. Note that this field cannot be set when spec.os.name is windows.", alias="appArmorProfile")
    fs_group: Optional[StrictInt] = Field(default=None, description="A special supplemental group that applies to all containers in a pod. Some volume types allow the Kubelet to change the ownership of that volume to be owned by the pod:  1. The owning GID will be the FSGroup 2. The setgid bit is set (new files created in the volume will be owned by FSGroup) 3. The permission bits are OR'd with rw-rw----  If unset, the Kubelet will not modify the ownership and permissions of any volume. Note that this field cannot be set when spec.os.name is windows.", alias="fsGroup")
    fs_group_change_policy: Optional[StrictStr] = Field(default=None, description="fsGroupChangePolicy defines behavior of changing ownership and permission of the volume before being exposed inside Pod. This field will only apply to volume types which support fsGroup based ownership(and permissions). It will have no effect on ephemeral volume types such as: secret, configmaps and emptydir. Valid values are \"OnRootMismatch\" and \"Always\". If not specified, \"Always\" is used. Note that this field cannot be set when spec.os.name is windows.  Possible enum values:  - `\"Always\"` indicates that volume's ownership and permissions should always be changed whenever volume is mounted inside a Pod. This the default behavior.  - `\"OnRootMismatch\"` indicates that volume's ownership and permissions will be changed only when permission and ownership of root directory does not match with expected permissions on the volume. This can help shorten the time it takes to change ownership and permissions of a volume.", alias="fsGroupChangePolicy")
    run_as_group: Optional[StrictInt] = Field(default=None, description="The GID to run the entrypoint of the container process. Uses runtime default if unset. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.", alias="runAsGroup")
    run_as_non_root: Optional[StrictBool] = Field(default=None, description="Indicates that the container must run as a non-root user. If true, the Kubelet will validate the image at runtime to ensure that it does not run as UID 0 (root) and fail to start the container if it does. If unset or false, no such validation will be performed. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence.", alias="runAsNonRoot")
    run_as_user: Optional[StrictInt] = Field(default=None, description="The UID to run the entrypoint of the container process. Defaults to user specified in image metadata if unspecified. May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.", alias="runAsUser")
    se_linux_change_policy: Optional[StrictStr] = Field(default=None, description="seLinuxChangePolicy defines how the container's SELinux label is applied to all volumes used by the Pod. It has no effect on nodes that do not support SELinux or to volumes does not support SELinux. Valid values are \"MountOption\" and \"Recursive\".  \"Recursive\" means relabeling of all files on all Pod volumes by the container runtime. This may be slow for large volumes, but allows mixing privileged and unprivileged Pods sharing the same volume on the same node.  \"MountOption\" mounts all eligible Pod volumes with `-o context` mount option. This requires all Pods that share the same volume to use the same SELinux label. It is not possible to share the same volume among privileged and unprivileged Pods. Eligible volumes are in-tree FibreChannel and iSCSI volumes, and all CSI volumes whose CSI driver announces SELinux support by setting spec.seLinuxMount: true in their CSIDriver instance. Other volumes are always re-labelled recursively. \"MountOption\" value is allowed only when SELinuxMount feature gate is enabled.  If not specified and SELinuxMount feature gate is enabled, \"MountOption\" is used. If not specified and SELinuxMount feature gate is disabled, \"MountOption\" is used for ReadWriteOncePod volumes and \"Recursive\" for all other volumes.  This field affects only Pods that have SELinux label set, either in PodSecurityContext or in SecurityContext of all containers.  All Pods that use the same volume should use the same seLinuxChangePolicy, otherwise some pods can get stuck in ContainerCreating state. Note that this field cannot be set when spec.os.name is windows.", alias="seLinuxChangePolicy")
    se_linux_options: Optional[IoK8sApiCoreV1SELinuxOptions] = Field(default=None, description="The SELinux context to be applied to all containers. If unspecified, the container runtime will allocate a random SELinux context for each container.  May also be set in SecurityContext.  If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence for that container. Note that this field cannot be set when spec.os.name is windows.", alias="seLinuxOptions")
    seccomp_profile: Optional[IoK8sApiCoreV1SeccompProfile] = Field(default=None, description="The seccomp options to use by the containers in this pod. Note that this field cannot be set when spec.os.name is windows.", alias="seccompProfile")
    supplemental_groups: Optional[List[StrictInt]] = Field(default=None, description="A list of groups applied to the first process run in each container, in addition to the container's primary GID and fsGroup (if specified).  If the SupplementalGroupsPolicy feature is enabled, the supplementalGroupsPolicy field determines whether these are in addition to or instead of any group memberships defined in the container image. If unspecified, no additional groups are added, though group memberships defined in the container image may still be used, depending on the supplementalGroupsPolicy field. Note that this field cannot be set when spec.os.name is windows.", alias="supplementalGroups")
    supplemental_groups_policy: Optional[StrictStr] = Field(default=None, description="Defines how supplemental groups of the first container processes are calculated. Valid values are \"Merge\" and \"Strict\". If not specified, \"Merge\" is used. (Alpha) Using the field requires the SupplementalGroupsPolicy feature gate to be enabled and the container runtime must implement support for this feature. Note that this field cannot be set when spec.os.name is windows.  Possible enum values:  - `\"Merge\"` means that the container's provided SupplementalGroups and FsGroup (specified in SecurityContext) will be merged with the primary user's groups as defined in the container image (in /etc/group).  - `\"Strict\"` means that the container's provided SupplementalGroups and FsGroup (specified in SecurityContext) will be used instead of any groups defined in the container image.", alias="supplementalGroupsPolicy")
    sysctls: Optional[List[IoK8sApiCoreV1Sysctl]] = Field(default=None, description="Sysctls hold a list of namespaced sysctls used for the pod. Pods with unsupported sysctls (by the container runtime) might fail to launch. Note that this field cannot be set when spec.os.name is windows.")
    windows_options: Optional[IoK8sApiCoreV1WindowsSecurityContextOptions] = Field(default=None, description="The Windows specific settings applied to all containers. If unspecified, the options within a container's SecurityContext will be used. If set in both SecurityContext and PodSecurityContext, the value specified in SecurityContext takes precedence. Note that this field cannot be set when spec.os.name is linux.", alias="windowsOptions")
    __properties: ClassVar[List[str]] = ["appArmorProfile", "fsGroup", "fsGroupChangePolicy", "runAsGroup", "runAsNonRoot", "runAsUser", "seLinuxChangePolicy", "seLinuxOptions", "seccompProfile", "supplementalGroups", "supplementalGroupsPolicy", "sysctls", "windowsOptions"]

    @field_validator('fs_group_change_policy')
    def fs_group_change_policy_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['Always', 'OnRootMismatch']):
            raise ValueError("must be one of enum values ('Always', 'OnRootMismatch')")
        return value

    @field_validator('supplemental_groups_policy')
    def supplemental_groups_policy_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['Merge', 'Strict']):
            raise ValueError("must be one of enum values ('Merge', 'Strict')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1PodSecurityContext from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of app_armor_profile
        if self.app_armor_profile:
            _dict['appArmorProfile'] = self.app_armor_profile.to_dict()
        # override the default output from pydantic by calling `to_dict()` of se_linux_options
        if self.se_linux_options:
            _dict['seLinuxOptions'] = self.se_linux_options.to_dict()
        # override the default output from pydantic by calling `to_dict()` of seccomp_profile
        if self.seccomp_profile:
            _dict['seccompProfile'] = self.seccomp_profile.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in sysctls (list)
        _items = []
        if self.sysctls:
            for _item_sysctls in self.sysctls:
                if _item_sysctls:
                    _items.append(_item_sysctls.to_dict())
            _dict['sysctls'] = _items
        # override the default output from pydantic by calling `to_dict()` of windows_options
        if self.windows_options:
            _dict['windowsOptions'] = self.windows_options.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1PodSecurityContext from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "appArmorProfile": IoK8sApiCoreV1AppArmorProfile.from_dict(obj["appArmorProfile"]) if obj.get("appArmorProfile") is not None else None,
            "fsGroup": obj.get("fsGroup"),
            "fsGroupChangePolicy": obj.get("fsGroupChangePolicy"),
            "runAsGroup": obj.get("runAsGroup"),
            "runAsNonRoot": obj.get("runAsNonRoot"),
            "runAsUser": obj.get("runAsUser"),
            "seLinuxChangePolicy": obj.get("seLinuxChangePolicy"),
            "seLinuxOptions": IoK8sApiCoreV1SELinuxOptions.from_dict(obj["seLinuxOptions"]) if obj.get("seLinuxOptions") is not None else None,
            "seccompProfile": IoK8sApiCoreV1SeccompProfile.from_dict(obj["seccompProfile"]) if obj.get("seccompProfile") is not None else None,
            "supplementalGroups": obj.get("supplementalGroups"),
            "supplementalGroupsPolicy": obj.get("supplementalGroupsPolicy"),
            "sysctls": [IoK8sApiCoreV1Sysctl.from_dict(_item) for _item in obj["sysctls"]] if obj.get("sysctls") is not None else None,
            "windowsOptions": IoK8sApiCoreV1WindowsSecurityContextOptions.from_dict(obj["windowsOptions"]) if obj.get("windowsOptions") is not None else None
        })
        return _obj


