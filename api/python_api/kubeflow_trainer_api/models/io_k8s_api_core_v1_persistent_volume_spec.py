# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_aws_elastic_block_store_volume_source import IoK8sApiCoreV1AWSElasticBlockStoreVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_azure_disk_volume_source import IoK8sApiCoreV1AzureDiskVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_azure_file_persistent_volume_source import IoK8sApiCoreV1AzureFilePersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_ceph_fs_persistent_volume_source import IoK8sApiCoreV1CephFSPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_cinder_persistent_volume_source import IoK8sApiCoreV1CinderPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_csi_persistent_volume_source import IoK8sApiCoreV1CSIPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_fc_volume_source import IoK8sApiCoreV1FCVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_flex_persistent_volume_source import IoK8sApiCoreV1FlexPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_flocker_volume_source import IoK8sApiCoreV1FlockerVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_gce_persistent_disk_volume_source import IoK8sApiCoreV1GCEPersistentDiskVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_glusterfs_persistent_volume_source import IoK8sApiCoreV1GlusterfsPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_host_path_volume_source import IoK8sApiCoreV1HostPathVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_iscsi_persistent_volume_source import IoK8sApiCoreV1ISCSIPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_local_volume_source import IoK8sApiCoreV1LocalVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_nfs_volume_source import IoK8sApiCoreV1NFSVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_object_reference import IoK8sApiCoreV1ObjectReference
from kubeflow_trainer_api.models.io_k8s_api_core_v1_photon_persistent_disk_volume_source import IoK8sApiCoreV1PhotonPersistentDiskVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_portworx_volume_source import IoK8sApiCoreV1PortworxVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_quobyte_volume_source import IoK8sApiCoreV1QuobyteVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_rbd_persistent_volume_source import IoK8sApiCoreV1RBDPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_scale_io_persistent_volume_source import IoK8sApiCoreV1ScaleIOPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_storage_os_persistent_volume_source import IoK8sApiCoreV1StorageOSPersistentVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_volume_node_affinity import IoK8sApiCoreV1VolumeNodeAffinity
from kubeflow_trainer_api.models.io_k8s_api_core_v1_vsphere_virtual_disk_volume_source import IoK8sApiCoreV1VsphereVirtualDiskVolumeSource
from kubeflow_trainer_api.models.io_k8s_apimachinery_pkg_api_resource_quantity import IoK8sApimachineryPkgApiResourceQuantity
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1PersistentVolumeSpec(BaseModel):
    """
    PersistentVolumeSpec is the specification of a persistent volume.
    """ # noqa: E501
    access_modes: Optional[List[StrictStr]] = Field(default=None, description="accessModes contains all ways the volume can be mounted. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes", alias="accessModes")
    aws_elastic_block_store: Optional[IoK8sApiCoreV1AWSElasticBlockStoreVolumeSource] = Field(default=None, description="awsElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Deprecated: AWSElasticBlockStore is deprecated. All operations for the in-tree awsElasticBlockStore type are redirected to the ebs.csi.aws.com CSI driver. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore", alias="awsElasticBlockStore")
    azure_disk: Optional[IoK8sApiCoreV1AzureDiskVolumeSource] = Field(default=None, description="azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod. Deprecated: AzureDisk is deprecated. All operations for the in-tree azureDisk type are redirected to the disk.csi.azure.com CSI driver.", alias="azureDisk")
    azure_file: Optional[IoK8sApiCoreV1AzureFilePersistentVolumeSource] = Field(default=None, description="azureFile represents an Azure File Service mount on the host and bind mount to the pod. Deprecated: AzureFile is deprecated. All operations for the in-tree azureFile type are redirected to the file.csi.azure.com CSI driver.", alias="azureFile")
    capacity: Optional[Dict[str, IoK8sApimachineryPkgApiResourceQuantity]] = Field(default=None, description="capacity is the description of the persistent volume's resources and capacity. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#capacity")
    cephfs: Optional[IoK8sApiCoreV1CephFSPersistentVolumeSource] = Field(default=None, description="cephFS represents a Ceph FS mount on the host that shares a pod's lifetime. Deprecated: CephFS is deprecated and the in-tree cephfs type is no longer supported.")
    cinder: Optional[IoK8sApiCoreV1CinderPersistentVolumeSource] = Field(default=None, description="cinder represents a cinder volume attached and mounted on kubelets host machine. Deprecated: Cinder is deprecated. All operations for the in-tree cinder type are redirected to the cinder.csi.openstack.org CSI driver. More info: https://examples.k8s.io/mysql-cinder-pd/README.md")
    claim_ref: Optional[IoK8sApiCoreV1ObjectReference] = Field(default=None, description="claimRef is part of a bi-directional binding between PersistentVolume and PersistentVolumeClaim. Expected to be non-nil when bound. claim.VolumeName is the authoritative bind between PV and PVC. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#binding", alias="claimRef")
    csi: Optional[IoK8sApiCoreV1CSIPersistentVolumeSource] = Field(default=None, description="csi represents storage that is handled by an external CSI driver.")
    fc: Optional[IoK8sApiCoreV1FCVolumeSource] = Field(default=None, description="fc represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.")
    flex_volume: Optional[IoK8sApiCoreV1FlexPersistentVolumeSource] = Field(default=None, description="flexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin. Deprecated: FlexVolume is deprecated. Consider using a CSIDriver instead.", alias="flexVolume")
    flocker: Optional[IoK8sApiCoreV1FlockerVolumeSource] = Field(default=None, description="flocker represents a Flocker volume attached to a kubelet's host machine and exposed to the pod for its usage. This depends on the Flocker control service being running. Deprecated: Flocker is deprecated and the in-tree flocker type is no longer supported.")
    gce_persistent_disk: Optional[IoK8sApiCoreV1GCEPersistentDiskVolumeSource] = Field(default=None, description="gcePersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Provisioned by an admin. Deprecated: GCEPersistentDisk is deprecated. All operations for the in-tree gcePersistentDisk type are redirected to the pd.csi.storage.gke.io CSI driver. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk", alias="gcePersistentDisk")
    glusterfs: Optional[IoK8sApiCoreV1GlusterfsPersistentVolumeSource] = Field(default=None, description="glusterfs represents a Glusterfs volume that is attached to a host and exposed to the pod. Provisioned by an admin. Deprecated: Glusterfs is deprecated and the in-tree glusterfs type is no longer supported. More info: https://examples.k8s.io/volumes/glusterfs/README.md")
    host_path: Optional[IoK8sApiCoreV1HostPathVolumeSource] = Field(default=None, description="hostPath represents a directory on the host. Provisioned by a developer or tester. This is useful for single-node development and testing only! On-host storage is not supported in any way and WILL NOT WORK in a multi-node cluster. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath", alias="hostPath")
    iscsi: Optional[IoK8sApiCoreV1ISCSIPersistentVolumeSource] = Field(default=None, description="iscsi represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Provisioned by an admin.")
    local: Optional[IoK8sApiCoreV1LocalVolumeSource] = Field(default=None, description="local represents directly-attached storage with node affinity")
    mount_options: Optional[List[StrictStr]] = Field(default=None, description="mountOptions is the list of mount options, e.g. [\"ro\", \"soft\"]. Not validated - mount will simply fail if one is invalid. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes/#mount-options", alias="mountOptions")
    nfs: Optional[IoK8sApiCoreV1NFSVolumeSource] = Field(default=None, description="nfs represents an NFS mount on the host. Provisioned by an admin. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs")
    node_affinity: Optional[IoK8sApiCoreV1VolumeNodeAffinity] = Field(default=None, description="nodeAffinity defines constraints that limit what nodes this volume can be accessed from. This field influences the scheduling of pods that use this volume.", alias="nodeAffinity")
    persistent_volume_reclaim_policy: Optional[StrictStr] = Field(default=None, description="persistentVolumeReclaimPolicy defines what happens to a persistent volume when released from its claim. Valid options are Retain (default for manually created PersistentVolumes), Delete (default for dynamically provisioned PersistentVolumes), and Recycle (deprecated). Recycle must be supported by the volume plugin underlying this PersistentVolume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#reclaiming  Possible enum values:  - `\"Delete\"` means the volume will be deleted from Kubernetes on release from its claim. The volume plugin must support Deletion.  - `\"Recycle\"` means the volume will be recycled back into the pool of unbound persistent volumes on release from its claim. The volume plugin must support Recycling.  - `\"Retain\"` means the volume will be left in its current phase (Released) for manual reclamation by the administrator. The default policy is Retain.", alias="persistentVolumeReclaimPolicy")
    photon_persistent_disk: Optional[IoK8sApiCoreV1PhotonPersistentDiskVolumeSource] = Field(default=None, description="photonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine. Deprecated: PhotonPersistentDisk is deprecated and the in-tree photonPersistentDisk type is no longer supported.", alias="photonPersistentDisk")
    portworx_volume: Optional[IoK8sApiCoreV1PortworxVolumeSource] = Field(default=None, description="portworxVolume represents a portworx volume attached and mounted on kubelets host machine. Deprecated: PortworxVolume is deprecated. All operations for the in-tree portworxVolume type are redirected to the pxd.portworx.com CSI driver when the CSIMigrationPortworx feature-gate is on.", alias="portworxVolume")
    quobyte: Optional[IoK8sApiCoreV1QuobyteVolumeSource] = Field(default=None, description="quobyte represents a Quobyte mount on the host that shares a pod's lifetime. Deprecated: Quobyte is deprecated and the in-tree quobyte type is no longer supported.")
    rbd: Optional[IoK8sApiCoreV1RBDPersistentVolumeSource] = Field(default=None, description="rbd represents a Rados Block Device mount on the host that shares a pod's lifetime. Deprecated: RBD is deprecated and the in-tree rbd type is no longer supported. More info: https://examples.k8s.io/volumes/rbd/README.md")
    scale_io: Optional[IoK8sApiCoreV1ScaleIOPersistentVolumeSource] = Field(default=None, description="scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes. Deprecated: ScaleIO is deprecated and the in-tree scaleIO type is no longer supported.", alias="scaleIO")
    storage_class_name: Optional[StrictStr] = Field(default=None, description="storageClassName is the name of StorageClass to which this persistent volume belongs. Empty value means that this volume does not belong to any StorageClass.", alias="storageClassName")
    storageos: Optional[IoK8sApiCoreV1StorageOSPersistentVolumeSource] = Field(default=None, description="storageOS represents a StorageOS volume that is attached to the kubelet's host machine and mounted into the pod. Deprecated: StorageOS is deprecated and the in-tree storageos type is no longer supported. More info: https://examples.k8s.io/volumes/storageos/README.md")
    volume_attributes_class_name: Optional[StrictStr] = Field(default=None, description="Name of VolumeAttributesClass to which this persistent volume belongs. Empty value is not allowed. When this field is not set, it indicates that this volume does not belong to any VolumeAttributesClass. This field is mutable and can be changed by the CSI driver after a volume has been updated successfully to a new class. For an unbound PersistentVolume, the volumeAttributesClassName will be matched with unbound PersistentVolumeClaims during the binding process. This is a beta field and requires enabling VolumeAttributesClass feature (off by default).", alias="volumeAttributesClassName")
    volume_mode: Optional[StrictStr] = Field(default=None, description="volumeMode defines if a volume is intended to be used with a formatted filesystem or to remain in raw block state. Value of Filesystem is implied when not included in spec.  Possible enum values:  - `\"Block\"` means the volume will not be formatted with a filesystem and will remain a raw block device.  - `\"Filesystem\"` means the volume will be or is formatted with a filesystem.", alias="volumeMode")
    vsphere_volume: Optional[IoK8sApiCoreV1VsphereVirtualDiskVolumeSource] = Field(default=None, description="vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine. Deprecated: VsphereVolume is deprecated. All operations for the in-tree vsphereVolume type are redirected to the csi.vsphere.vmware.com CSI driver.", alias="vsphereVolume")
    __properties: ClassVar[List[str]] = ["accessModes", "awsElasticBlockStore", "azureDisk", "azureFile", "capacity", "cephfs", "cinder", "claimRef", "csi", "fc", "flexVolume", "flocker", "gcePersistentDisk", "glusterfs", "hostPath", "iscsi", "local", "mountOptions", "nfs", "nodeAffinity", "persistentVolumeReclaimPolicy", "photonPersistentDisk", "portworxVolume", "quobyte", "rbd", "scaleIO", "storageClassName", "storageos", "volumeAttributesClassName", "volumeMode", "vsphereVolume"]

    @field_validator('access_modes')
    def access_modes_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        for i in value:
            if i not in set(['ReadOnlyMany', 'ReadWriteMany', 'ReadWriteOnce', 'ReadWriteOncePod']):
                raise ValueError("each list item must be one of ('ReadOnlyMany', 'ReadWriteMany', 'ReadWriteOnce', 'ReadWriteOncePod')")
        return value

    @field_validator('persistent_volume_reclaim_policy')
    def persistent_volume_reclaim_policy_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['Delete', 'Recycle', 'Retain']):
            raise ValueError("must be one of enum values ('Delete', 'Recycle', 'Retain')")
        return value

    @field_validator('volume_mode')
    def volume_mode_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['Block', 'Filesystem']):
            raise ValueError("must be one of enum values ('Block', 'Filesystem')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1PersistentVolumeSpec from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of aws_elastic_block_store
        if self.aws_elastic_block_store:
            _dict['awsElasticBlockStore'] = self.aws_elastic_block_store.to_dict()
        # override the default output from pydantic by calling `to_dict()` of azure_disk
        if self.azure_disk:
            _dict['azureDisk'] = self.azure_disk.to_dict()
        # override the default output from pydantic by calling `to_dict()` of azure_file
        if self.azure_file:
            _dict['azureFile'] = self.azure_file.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each value in capacity (dict)
        _field_dict = {}
        if self.capacity:
            for _key_capacity in self.capacity:
                if self.capacity[_key_capacity]:
                    _field_dict[_key_capacity] = self.capacity[_key_capacity].to_dict()
            _dict['capacity'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of cephfs
        if self.cephfs:
            _dict['cephfs'] = self.cephfs.to_dict()
        # override the default output from pydantic by calling `to_dict()` of cinder
        if self.cinder:
            _dict['cinder'] = self.cinder.to_dict()
        # override the default output from pydantic by calling `to_dict()` of claim_ref
        if self.claim_ref:
            _dict['claimRef'] = self.claim_ref.to_dict()
        # override the default output from pydantic by calling `to_dict()` of csi
        if self.csi:
            _dict['csi'] = self.csi.to_dict()
        # override the default output from pydantic by calling `to_dict()` of fc
        if self.fc:
            _dict['fc'] = self.fc.to_dict()
        # override the default output from pydantic by calling `to_dict()` of flex_volume
        if self.flex_volume:
            _dict['flexVolume'] = self.flex_volume.to_dict()
        # override the default output from pydantic by calling `to_dict()` of flocker
        if self.flocker:
            _dict['flocker'] = self.flocker.to_dict()
        # override the default output from pydantic by calling `to_dict()` of gce_persistent_disk
        if self.gce_persistent_disk:
            _dict['gcePersistentDisk'] = self.gce_persistent_disk.to_dict()
        # override the default output from pydantic by calling `to_dict()` of glusterfs
        if self.glusterfs:
            _dict['glusterfs'] = self.glusterfs.to_dict()
        # override the default output from pydantic by calling `to_dict()` of host_path
        if self.host_path:
            _dict['hostPath'] = self.host_path.to_dict()
        # override the default output from pydantic by calling `to_dict()` of iscsi
        if self.iscsi:
            _dict['iscsi'] = self.iscsi.to_dict()
        # override the default output from pydantic by calling `to_dict()` of local
        if self.local:
            _dict['local'] = self.local.to_dict()
        # override the default output from pydantic by calling `to_dict()` of nfs
        if self.nfs:
            _dict['nfs'] = self.nfs.to_dict()
        # override the default output from pydantic by calling `to_dict()` of node_affinity
        if self.node_affinity:
            _dict['nodeAffinity'] = self.node_affinity.to_dict()
        # override the default output from pydantic by calling `to_dict()` of photon_persistent_disk
        if self.photon_persistent_disk:
            _dict['photonPersistentDisk'] = self.photon_persistent_disk.to_dict()
        # override the default output from pydantic by calling `to_dict()` of portworx_volume
        if self.portworx_volume:
            _dict['portworxVolume'] = self.portworx_volume.to_dict()
        # override the default output from pydantic by calling `to_dict()` of quobyte
        if self.quobyte:
            _dict['quobyte'] = self.quobyte.to_dict()
        # override the default output from pydantic by calling `to_dict()` of rbd
        if self.rbd:
            _dict['rbd'] = self.rbd.to_dict()
        # override the default output from pydantic by calling `to_dict()` of scale_io
        if self.scale_io:
            _dict['scaleIO'] = self.scale_io.to_dict()
        # override the default output from pydantic by calling `to_dict()` of storageos
        if self.storageos:
            _dict['storageos'] = self.storageos.to_dict()
        # override the default output from pydantic by calling `to_dict()` of vsphere_volume
        if self.vsphere_volume:
            _dict['vsphereVolume'] = self.vsphere_volume.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1PersistentVolumeSpec from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "accessModes": obj.get("accessModes"),
            "awsElasticBlockStore": IoK8sApiCoreV1AWSElasticBlockStoreVolumeSource.from_dict(obj["awsElasticBlockStore"]) if obj.get("awsElasticBlockStore") is not None else None,
            "azureDisk": IoK8sApiCoreV1AzureDiskVolumeSource.from_dict(obj["azureDisk"]) if obj.get("azureDisk") is not None else None,
            "azureFile": IoK8sApiCoreV1AzureFilePersistentVolumeSource.from_dict(obj["azureFile"]) if obj.get("azureFile") is not None else None,
            "capacity": dict(
                (_k, IoK8sApimachineryPkgApiResourceQuantity.from_dict(_v))
                for _k, _v in obj["capacity"].items()
            )
            if obj.get("capacity") is not None
            else None,
            "cephfs": IoK8sApiCoreV1CephFSPersistentVolumeSource.from_dict(obj["cephfs"]) if obj.get("cephfs") is not None else None,
            "cinder": IoK8sApiCoreV1CinderPersistentVolumeSource.from_dict(obj["cinder"]) if obj.get("cinder") is not None else None,
            "claimRef": IoK8sApiCoreV1ObjectReference.from_dict(obj["claimRef"]) if obj.get("claimRef") is not None else None,
            "csi": IoK8sApiCoreV1CSIPersistentVolumeSource.from_dict(obj["csi"]) if obj.get("csi") is not None else None,
            "fc": IoK8sApiCoreV1FCVolumeSource.from_dict(obj["fc"]) if obj.get("fc") is not None else None,
            "flexVolume": IoK8sApiCoreV1FlexPersistentVolumeSource.from_dict(obj["flexVolume"]) if obj.get("flexVolume") is not None else None,
            "flocker": IoK8sApiCoreV1FlockerVolumeSource.from_dict(obj["flocker"]) if obj.get("flocker") is not None else None,
            "gcePersistentDisk": IoK8sApiCoreV1GCEPersistentDiskVolumeSource.from_dict(obj["gcePersistentDisk"]) if obj.get("gcePersistentDisk") is not None else None,
            "glusterfs": IoK8sApiCoreV1GlusterfsPersistentVolumeSource.from_dict(obj["glusterfs"]) if obj.get("glusterfs") is not None else None,
            "hostPath": IoK8sApiCoreV1HostPathVolumeSource.from_dict(obj["hostPath"]) if obj.get("hostPath") is not None else None,
            "iscsi": IoK8sApiCoreV1ISCSIPersistentVolumeSource.from_dict(obj["iscsi"]) if obj.get("iscsi") is not None else None,
            "local": IoK8sApiCoreV1LocalVolumeSource.from_dict(obj["local"]) if obj.get("local") is not None else None,
            "mountOptions": obj.get("mountOptions"),
            "nfs": IoK8sApiCoreV1NFSVolumeSource.from_dict(obj["nfs"]) if obj.get("nfs") is not None else None,
            "nodeAffinity": IoK8sApiCoreV1VolumeNodeAffinity.from_dict(obj["nodeAffinity"]) if obj.get("nodeAffinity") is not None else None,
            "persistentVolumeReclaimPolicy": obj.get("persistentVolumeReclaimPolicy"),
            "photonPersistentDisk": IoK8sApiCoreV1PhotonPersistentDiskVolumeSource.from_dict(obj["photonPersistentDisk"]) if obj.get("photonPersistentDisk") is not None else None,
            "portworxVolume": IoK8sApiCoreV1PortworxVolumeSource.from_dict(obj["portworxVolume"]) if obj.get("portworxVolume") is not None else None,
            "quobyte": IoK8sApiCoreV1QuobyteVolumeSource.from_dict(obj["quobyte"]) if obj.get("quobyte") is not None else None,
            "rbd": IoK8sApiCoreV1RBDPersistentVolumeSource.from_dict(obj["rbd"]) if obj.get("rbd") is not None else None,
            "scaleIO": IoK8sApiCoreV1ScaleIOPersistentVolumeSource.from_dict(obj["scaleIO"]) if obj.get("scaleIO") is not None else None,
            "storageClassName": obj.get("storageClassName"),
            "storageos": IoK8sApiCoreV1StorageOSPersistentVolumeSource.from_dict(obj["storageos"]) if obj.get("storageos") is not None else None,
            "volumeAttributesClassName": obj.get("volumeAttributesClassName"),
            "volumeMode": obj.get("volumeMode"),
            "vsphereVolume": IoK8sApiCoreV1VsphereVirtualDiskVolumeSource.from_dict(obj["vsphereVolume"]) if obj.get("vsphereVolume") is not None else None
        })
        return _obj


