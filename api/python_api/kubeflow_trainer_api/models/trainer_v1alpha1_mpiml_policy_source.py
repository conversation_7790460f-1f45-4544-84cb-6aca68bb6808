# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class TrainerV1alpha1MPIMLPolicySource(BaseModel):
    """
    MPIMLPolicySource represents a MPI runtime configuration.
    """ # noqa: E501
    mpi_implementation: Optional[StrictStr] = Field(default=None, description="Implementation name for the MPI to create the appropriate hostfile. Defaults to OpenMPI.", alias="mpiImplementation")
    num_proc_per_node: Optional[StrictInt] = Field(default=None, description="Number of processes per node. This value is equal to the number of slots for each node in the hostfile. Defaults to 1.", alias="numProcPerNode")
    run_launcher_as_node: Optional[StrictBool] = Field(default=None, description="Whether to run training process on the launcher Job. Defaults to false.", alias="runLauncherAsNode")
    ssh_auth_mount_path: Optional[StrictStr] = Field(default=None, description="Directory where SSH keys are mounted. Defaults to /root/.ssh.", alias="sshAuthMountPath")
    __properties: ClassVar[List[str]] = ["mpiImplementation", "numProcPerNode", "runLauncherAsNode", "sshAuthMountPath"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of TrainerV1alpha1MPIMLPolicySource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of TrainerV1alpha1MPIMLPolicySource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "mpiImplementation": obj.get("mpiImplementation"),
            "numProcPerNode": obj.get("numProcPerNode"),
            "runLauncherAsNode": obj.get("runLauncherAsNode"),
            "sshAuthMountPath": obj.get("sshAuthMountPath")
        })
        return _obj


