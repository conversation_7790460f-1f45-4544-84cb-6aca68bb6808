# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictInt
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_autoscaling_v2_cross_version_object_reference import IoK8sApiAutoscalingV2CrossVersionObjectReference
from kubeflow_trainer_api.models.io_k8s_api_autoscaling_v2_horizontal_pod_autoscaler_behavior import IoK8sApiAutoscalingV2HorizontalPodAutoscalerBehavior
from kubeflow_trainer_api.models.io_k8s_api_autoscaling_v2_metric_spec import IoK8sApiAutoscalingV2MetricSpec
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiAutoscalingV2HorizontalPodAutoscalerSpec(BaseModel):
    """
    HorizontalPodAutoscalerSpec describes the desired functionality of the HorizontalPodAutoscaler.
    """ # noqa: E501
    behavior: Optional[IoK8sApiAutoscalingV2HorizontalPodAutoscalerBehavior] = Field(default=None, description="behavior configures the scaling behavior of the target in both Up and Down directions (scaleUp and scaleDown fields respectively). If not set, the default HPAScalingRules for scale up and scale down are used.")
    max_replicas: StrictInt = Field(description="maxReplicas is the upper limit for the number of replicas to which the autoscaler can scale up. It cannot be less that minReplicas.", alias="maxReplicas")
    metrics: Optional[List[IoK8sApiAutoscalingV2MetricSpec]] = Field(default=None, description="metrics contains the specifications for which to use to calculate the desired replica count (the maximum replica count across all metrics will be used).  The desired replica count is calculated multiplying the ratio between the target value and the current value by the current number of pods.  Ergo, metrics used must decrease as the pod count is increased, and vice-versa.  See the individual metric source types for more information about how each type of metric must respond. If not set, the default metric will be set to 80% average CPU utilization.")
    min_replicas: Optional[StrictInt] = Field(default=None, description="minReplicas is the lower limit for the number of replicas to which the autoscaler can scale down.  It defaults to 1 pod.  minReplicas is allowed to be 0 if the alpha feature gate HPAScaleToZero is enabled and at least one Object or External metric is configured.  Scaling is active as long as at least one metric value is available.", alias="minReplicas")
    scale_target_ref: IoK8sApiAutoscalingV2CrossVersionObjectReference = Field(description="scaleTargetRef points to the target resource to scale, and is used to the pods for which metrics should be collected, as well as to actually change the replica count.", alias="scaleTargetRef")
    __properties: ClassVar[List[str]] = ["behavior", "maxReplicas", "metrics", "minReplicas", "scaleTargetRef"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiAutoscalingV2HorizontalPodAutoscalerSpec from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of behavior
        if self.behavior:
            _dict['behavior'] = self.behavior.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in metrics (list)
        _items = []
        if self.metrics:
            for _item_metrics in self.metrics:
                if _item_metrics:
                    _items.append(_item_metrics.to_dict())
            _dict['metrics'] = _items
        # override the default output from pydantic by calling `to_dict()` of scale_target_ref
        if self.scale_target_ref:
            _dict['scaleTargetRef'] = self.scale_target_ref.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiAutoscalingV2HorizontalPodAutoscalerSpec from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "behavior": IoK8sApiAutoscalingV2HorizontalPodAutoscalerBehavior.from_dict(obj["behavior"]) if obj.get("behavior") is not None else None,
            "maxReplicas": obj.get("maxReplicas") if obj.get("maxReplicas") is not None else 0,
            "metrics": [IoK8sApiAutoscalingV2MetricSpec.from_dict(_item) for _item in obj["metrics"]] if obj.get("metrics") is not None else None,
            "minReplicas": obj.get("minReplicas"),
            "scaleTargetRef": IoK8sApiAutoscalingV2CrossVersionObjectReference.from_dict(obj["scaleTargetRef"]) if obj.get("scaleTargetRef") is not None else None
        })
        return _obj


