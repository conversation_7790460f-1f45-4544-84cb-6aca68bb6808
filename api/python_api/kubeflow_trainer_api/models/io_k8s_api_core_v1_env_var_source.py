# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_config_map_key_selector import IoK8sApiCoreV1ConfigMapKeySelector
from kubeflow_trainer_api.models.io_k8s_api_core_v1_object_field_selector import IoK8sApiCoreV1ObjectFieldSelector
from kubeflow_trainer_api.models.io_k8s_api_core_v1_resource_field_selector import IoK8sApiCoreV1ResourceFieldSelector
from kubeflow_trainer_api.models.io_k8s_api_core_v1_secret_key_selector import IoK8sApiCoreV1SecretKeySelector
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1EnvVarSource(BaseModel):
    """
    EnvVarSource represents a source for the value of an EnvVar.
    """ # noqa: E501
    config_map_key_ref: Optional[IoK8sApiCoreV1ConfigMapKeySelector] = Field(default=None, description="Selects a key of a ConfigMap.", alias="configMapKeyRef")
    field_ref: Optional[IoK8sApiCoreV1ObjectFieldSelector] = Field(default=None, description="Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`, spec.nodeName, spec.serviceAccountName, status.hostIP, status.podIP, status.podIPs.", alias="fieldRef")
    resource_field_ref: Optional[IoK8sApiCoreV1ResourceFieldSelector] = Field(default=None, description="Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.ephemeral-storage) are currently supported.", alias="resourceFieldRef")
    secret_key_ref: Optional[IoK8sApiCoreV1SecretKeySelector] = Field(default=None, description="Selects a key of a secret in the pod's namespace", alias="secretKeyRef")
    __properties: ClassVar[List[str]] = ["configMapKeyRef", "fieldRef", "resourceFieldRef", "secretKeyRef"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1EnvVarSource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of config_map_key_ref
        if self.config_map_key_ref:
            _dict['configMapKeyRef'] = self.config_map_key_ref.to_dict()
        # override the default output from pydantic by calling `to_dict()` of field_ref
        if self.field_ref:
            _dict['fieldRef'] = self.field_ref.to_dict()
        # override the default output from pydantic by calling `to_dict()` of resource_field_ref
        if self.resource_field_ref:
            _dict['resourceFieldRef'] = self.resource_field_ref.to_dict()
        # override the default output from pydantic by calling `to_dict()` of secret_key_ref
        if self.secret_key_ref:
            _dict['secretKeyRef'] = self.secret_key_ref.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1EnvVarSource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "configMapKeyRef": IoK8sApiCoreV1ConfigMapKeySelector.from_dict(obj["configMapKeyRef"]) if obj.get("configMapKeyRef") is not None else None,
            "fieldRef": IoK8sApiCoreV1ObjectFieldSelector.from_dict(obj["fieldRef"]) if obj.get("fieldRef") is not None else None,
            "resourceFieldRef": IoK8sApiCoreV1ResourceFieldSelector.from_dict(obj["resourceFieldRef"]) if obj.get("resourceFieldRef") is not None else None,
            "secretKeyRef": IoK8sApiCoreV1SecretKeySelector.from_dict(obj["secretKeyRef"]) if obj.get("secretKeyRef") is not None else None
        })
        return _obj


