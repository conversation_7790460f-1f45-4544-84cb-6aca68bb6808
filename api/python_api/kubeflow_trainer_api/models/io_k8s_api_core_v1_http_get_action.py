# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_http_header import IoK8sApiCoreV1HTTPHeader
from kubeflow_trainer_api.models.io_k8s_apimachinery_pkg_util_intstr_int_or_string import IoK8sApimachineryPkgUtilIntstrIntOrString
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1HTTPGetAction(BaseModel):
    """
    HTTPGetAction describes an action based on HTTP Get requests.
    """ # noqa: E501
    host: Optional[StrictStr] = Field(default=None, description="Host name to connect to, defaults to the pod IP. You probably want to set \"Host\" in httpHeaders instead.")
    http_headers: Optional[List[IoK8sApiCoreV1HTTPHeader]] = Field(default=None, description="Custom headers to set in the request. HTTP allows repeated headers.", alias="httpHeaders")
    path: Optional[StrictStr] = Field(default=None, description="Path to access on the HTTP server.")
    port: IoK8sApimachineryPkgUtilIntstrIntOrString = Field(description="Name or number of the port to access on the container. Number must be in the range 1 to 65535. Name must be an IANA_SVC_NAME.")
    scheme: Optional[StrictStr] = Field(default=None, description="Scheme to use for connecting to the host. Defaults to HTTP.  Possible enum values:  - `\"HTTP\"` means that the scheme used will be http://  - `\"HTTPS\"` means that the scheme used will be https://")
    __properties: ClassVar[List[str]] = ["host", "httpHeaders", "path", "port", "scheme"]

    @field_validator('scheme')
    def scheme_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        if value not in set(['HTTP', 'HTTPS']):
            raise ValueError("must be one of enum values ('HTTP', 'HTTPS')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1HTTPGetAction from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in http_headers (list)
        _items = []
        if self.http_headers:
            for _item_http_headers in self.http_headers:
                if _item_http_headers:
                    _items.append(_item_http_headers.to_dict())
            _dict['httpHeaders'] = _items
        # override the default output from pydantic by calling `to_dict()` of port
        if self.port:
            _dict['port'] = self.port.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1HTTPGetAction from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "host": obj.get("host"),
            "httpHeaders": [IoK8sApiCoreV1HTTPHeader.from_dict(_item) for _item in obj["httpHeaders"]] if obj.get("httpHeaders") is not None else None,
            "path": obj.get("path"),
            "port": IoK8sApimachineryPkgUtilIntstrIntOrString.from_dict(obj["port"]) if obj.get("port") is not None else None,
            "scheme": obj.get("scheme")
        })
        return _obj


