# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApimachineryPkgApisMetaV1APIResource(BaseModel):
    """
    APIResource specifies the name of a resource and whether it is namespaced.
    """ # noqa: E501
    categories: Optional[List[StrictStr]] = Field(default=None, description="categories is a list of the grouped resources this resource belongs to (e.g. 'all')")
    group: Optional[StrictStr] = Field(default=None, description="group is the preferred group of the resource.  Empty implies the group of the containing resource list. For subresources, this may have a different value, for example: Scale\".")
    kind: StrictStr = Field(description="kind is the kind for the resource (e.g. 'Foo' is the kind for a resource 'foo')")
    name: StrictStr = Field(description="name is the plural name of the resource.")
    namespaced: StrictBool = Field(description="namespaced indicates if a resource is namespaced or not.")
    short_names: Optional[List[StrictStr]] = Field(default=None, description="shortNames is a list of suggested short names of the resource.", alias="shortNames")
    singular_name: StrictStr = Field(description="singularName is the singular name of the resource.  This allows clients to handle plural and singular opaquely. The singularName is more correct for reporting status on a single item and both singular and plural are allowed from the kubectl CLI interface.", alias="singularName")
    storage_version_hash: Optional[StrictStr] = Field(default=None, description="The hash value of the storage version, the version this resource is converted to when written to the data store. Value must be treated as opaque by clients. Only equality comparison on the value is valid. This is an alpha feature and may change or be removed in the future. The field is populated by the apiserver only if the StorageVersionHash feature gate is enabled. This field will remain optional even if it graduates.", alias="storageVersionHash")
    verbs: List[StrictStr] = Field(description="verbs is a list of supported kube verbs (this includes get, list, watch, create, update, patch, delete, deletecollection, and proxy)")
    version: Optional[StrictStr] = Field(default=None, description="version is the preferred version of the resource.  Empty implies the version of the containing resource list For subresources, this may have a different value, for example: v1 (while inside a v1beta1 version of the core resource's group)\".")
    __properties: ClassVar[List[str]] = ["categories", "group", "kind", "name", "namespaced", "shortNames", "singularName", "storageVersionHash", "verbs", "version"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApimachineryPkgApisMetaV1APIResource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApimachineryPkgApisMetaV1APIResource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "categories": obj.get("categories"),
            "group": obj.get("group"),
            "kind": obj.get("kind") if obj.get("kind") is not None else '',
            "name": obj.get("name") if obj.get("name") is not None else '',
            "namespaced": obj.get("namespaced") if obj.get("namespaced") is not None else False,
            "shortNames": obj.get("shortNames"),
            "singularName": obj.get("singularName") if obj.get("singularName") is not None else '',
            "storageVersionHash": obj.get("storageVersionHash"),
            "verbs": obj.get("verbs"),
            "version": obj.get("version")
        })
        return _obj


