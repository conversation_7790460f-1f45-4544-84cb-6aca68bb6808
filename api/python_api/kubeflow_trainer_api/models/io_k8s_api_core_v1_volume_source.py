# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_aws_elastic_block_store_volume_source import IoK8sApiCoreV1AWSElasticBlockStoreVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_azure_disk_volume_source import IoK8sApiCoreV1AzureDiskVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_azure_file_volume_source import IoK8sApiCoreV1AzureFileVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_ceph_fs_volume_source import IoK8sApiCoreV1CephFSVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_cinder_volume_source import IoK8sApiCoreV1CinderVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_config_map_volume_source import IoK8sApiCoreV1ConfigMapVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_csi_volume_source import IoK8sApiCoreV1CSIVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_downward_api_volume_source import IoK8sApiCoreV1DownwardAPIVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_empty_dir_volume_source import IoK8sApiCoreV1EmptyDirVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_ephemeral_volume_source import IoK8sApiCoreV1EphemeralVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_fc_volume_source import IoK8sApiCoreV1FCVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_flex_volume_source import IoK8sApiCoreV1FlexVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_flocker_volume_source import IoK8sApiCoreV1FlockerVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_gce_persistent_disk_volume_source import IoK8sApiCoreV1GCEPersistentDiskVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_git_repo_volume_source import IoK8sApiCoreV1GitRepoVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_glusterfs_volume_source import IoK8sApiCoreV1GlusterfsVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_host_path_volume_source import IoK8sApiCoreV1HostPathVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_image_volume_source import IoK8sApiCoreV1ImageVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_iscsi_volume_source import IoK8sApiCoreV1ISCSIVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_nfs_volume_source import IoK8sApiCoreV1NFSVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_persistent_volume_claim_volume_source import IoK8sApiCoreV1PersistentVolumeClaimVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_photon_persistent_disk_volume_source import IoK8sApiCoreV1PhotonPersistentDiskVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_portworx_volume_source import IoK8sApiCoreV1PortworxVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_projected_volume_source import IoK8sApiCoreV1ProjectedVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_quobyte_volume_source import IoK8sApiCoreV1QuobyteVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_rbd_volume_source import IoK8sApiCoreV1RBDVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_scale_io_volume_source import IoK8sApiCoreV1ScaleIOVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_secret_volume_source import IoK8sApiCoreV1SecretVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_storage_os_volume_source import IoK8sApiCoreV1StorageOSVolumeSource
from kubeflow_trainer_api.models.io_k8s_api_core_v1_vsphere_virtual_disk_volume_source import IoK8sApiCoreV1VsphereVirtualDiskVolumeSource
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1VolumeSource(BaseModel):
    """
    Represents the source of a volume to mount. Only one of its members may be specified.
    """ # noqa: E501
    aws_elastic_block_store: Optional[IoK8sApiCoreV1AWSElasticBlockStoreVolumeSource] = Field(default=None, description="awsElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Deprecated: AWSElasticBlockStore is deprecated. All operations for the in-tree awsElasticBlockStore type are redirected to the ebs.csi.aws.com CSI driver. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore", alias="awsElasticBlockStore")
    azure_disk: Optional[IoK8sApiCoreV1AzureDiskVolumeSource] = Field(default=None, description="azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod. Deprecated: AzureDisk is deprecated. All operations for the in-tree azureDisk type are redirected to the disk.csi.azure.com CSI driver.", alias="azureDisk")
    azure_file: Optional[IoK8sApiCoreV1AzureFileVolumeSource] = Field(default=None, description="azureFile represents an Azure File Service mount on the host and bind mount to the pod. Deprecated: AzureFile is deprecated. All operations for the in-tree azureFile type are redirected to the file.csi.azure.com CSI driver.", alias="azureFile")
    cephfs: Optional[IoK8sApiCoreV1CephFSVolumeSource] = Field(default=None, description="cephFS represents a Ceph FS mount on the host that shares a pod's lifetime. Deprecated: CephFS is deprecated and the in-tree cephfs type is no longer supported.")
    cinder: Optional[IoK8sApiCoreV1CinderVolumeSource] = Field(default=None, description="cinder represents a cinder volume attached and mounted on kubelets host machine. Deprecated: Cinder is deprecated. All operations for the in-tree cinder type are redirected to the cinder.csi.openstack.org CSI driver. More info: https://examples.k8s.io/mysql-cinder-pd/README.md")
    config_map: Optional[IoK8sApiCoreV1ConfigMapVolumeSource] = Field(default=None, description="configMap represents a configMap that should populate this volume", alias="configMap")
    csi: Optional[IoK8sApiCoreV1CSIVolumeSource] = Field(default=None, description="csi (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers.")
    downward_api: Optional[IoK8sApiCoreV1DownwardAPIVolumeSource] = Field(default=None, description="downwardAPI represents downward API about the pod that should populate this volume", alias="downwardAPI")
    empty_dir: Optional[IoK8sApiCoreV1EmptyDirVolumeSource] = Field(default=None, description="emptyDir represents a temporary directory that shares a pod's lifetime. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir", alias="emptyDir")
    ephemeral: Optional[IoK8sApiCoreV1EphemeralVolumeSource] = Field(default=None, description="ephemeral represents a volume that is handled by a cluster storage driver. The volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts, and deleted when the pod is removed.  Use this if: a) the volume is only needed while the pod runs, b) features of normal volumes like restoring from snapshot or capacity    tracking are needed, c) the storage driver is specified through a storage class, and d) the storage driver supports dynamic volume provisioning through    a PersistentVolumeClaim (see EphemeralVolumeSource for more    information on the connection between this volume type    and PersistentVolumeClaim).  Use PersistentVolumeClaim or one of the vendor-specific APIs for volumes that persist for longer than the lifecycle of an individual pod.  Use CSI for light-weight local ephemeral volumes if the CSI driver is meant to be used that way - see the documentation of the driver for more information.  A pod can use both types of ephemeral volumes and persistent volumes at the same time.")
    fc: Optional[IoK8sApiCoreV1FCVolumeSource] = Field(default=None, description="fc represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.")
    flex_volume: Optional[IoK8sApiCoreV1FlexVolumeSource] = Field(default=None, description="flexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin. Deprecated: FlexVolume is deprecated. Consider using a CSIDriver instead.", alias="flexVolume")
    flocker: Optional[IoK8sApiCoreV1FlockerVolumeSource] = Field(default=None, description="flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running. Deprecated: Flocker is deprecated and the in-tree flocker type is no longer supported.")
    gce_persistent_disk: Optional[IoK8sApiCoreV1GCEPersistentDiskVolumeSource] = Field(default=None, description="gcePersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. Deprecated: GCEPersistentDisk is deprecated. All operations for the in-tree gcePersistentDisk type are redirected to the pd.csi.storage.gke.io CSI driver. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk", alias="gcePersistentDisk")
    git_repo: Optional[IoK8sApiCoreV1GitRepoVolumeSource] = Field(default=None, description="gitRepo represents a git repository at a particular revision. Deprecated: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.", alias="gitRepo")
    glusterfs: Optional[IoK8sApiCoreV1GlusterfsVolumeSource] = Field(default=None, description="glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime. Deprecated: Glusterfs is deprecated and the in-tree glusterfs type is no longer supported. More info: https://examples.k8s.io/volumes/glusterfs/README.md")
    host_path: Optional[IoK8sApiCoreV1HostPathVolumeSource] = Field(default=None, description="hostPath represents a pre-existing file or directory on the host machine that is directly exposed to the container. This is generally used for system agents or other privileged things that are allowed to see the host machine. Most containers will NOT need this. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath", alias="hostPath")
    image: Optional[IoK8sApiCoreV1ImageVolumeSource] = Field(default=None, description="image represents an OCI object (a container image or artifact) pulled and mounted on the kubelet's host machine. The volume is resolved at pod startup depending on which PullPolicy value is provided:  - Always: the kubelet always attempts to pull the reference. Container creation will fail If the pull fails. - Never: the kubelet never pulls the reference and only uses a local image or artifact. Container creation will fail if the reference isn't present. - IfNotPresent: the kubelet pulls if the reference isn't already present on disk. Container creation will fail if the reference isn't present and the pull fails.  The volume gets re-resolved if the pod gets deleted and recreated, which means that new remote content will become available on pod recreation. A failure to resolve or pull the image during pod startup will block containers from starting and may add significant latency. Failures will be retried using normal volume backoff and will be reported on the pod reason and message. The types of objects that may be mounted by this volume are defined by the container runtime implementation on a host machine and at minimum must include all valid types supported by the container image field. The OCI object gets mounted in a single directory (spec.containers[*].volumeMounts.mountPath) by merging the manifest layers in the same way as for container images. The volume will be mounted read-only (ro) and non-executable files (noexec). Sub path mounts for containers are not supported (spec.containers[*].volumeMounts.subpath). The field spec.securityContext.fsGroupChangePolicy has no effect on this volume type.")
    iscsi: Optional[IoK8sApiCoreV1ISCSIVolumeSource] = Field(default=None, description="iscsi represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://examples.k8s.io/volumes/iscsi/README.md")
    nfs: Optional[IoK8sApiCoreV1NFSVolumeSource] = Field(default=None, description="nfs represents an NFS mount on the host that shares a pod's lifetime More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs")
    persistent_volume_claim: Optional[IoK8sApiCoreV1PersistentVolumeClaimVolumeSource] = Field(default=None, description="persistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims", alias="persistentVolumeClaim")
    photon_persistent_disk: Optional[IoK8sApiCoreV1PhotonPersistentDiskVolumeSource] = Field(default=None, description="photonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine. Deprecated: PhotonPersistentDisk is deprecated and the in-tree photonPersistentDisk type is no longer supported.", alias="photonPersistentDisk")
    portworx_volume: Optional[IoK8sApiCoreV1PortworxVolumeSource] = Field(default=None, description="portworxVolume represents a portworx volume attached and mounted on kubelets host machine. Deprecated: PortworxVolume is deprecated. All operations for the in-tree portworxVolume type are redirected to the pxd.portworx.com CSI driver when the CSIMigrationPortworx feature-gate is on.", alias="portworxVolume")
    projected: Optional[IoK8sApiCoreV1ProjectedVolumeSource] = Field(default=None, description="projected items for all in one resources secrets, configmaps, and downward API")
    quobyte: Optional[IoK8sApiCoreV1QuobyteVolumeSource] = Field(default=None, description="quobyte represents a Quobyte mount on the host that shares a pod's lifetime. Deprecated: Quobyte is deprecated and the in-tree quobyte type is no longer supported.")
    rbd: Optional[IoK8sApiCoreV1RBDVolumeSource] = Field(default=None, description="rbd represents a Rados Block Device mount on the host that shares a pod's lifetime. Deprecated: RBD is deprecated and the in-tree rbd type is no longer supported. More info: https://examples.k8s.io/volumes/rbd/README.md")
    scale_io: Optional[IoK8sApiCoreV1ScaleIOVolumeSource] = Field(default=None, description="scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes. Deprecated: ScaleIO is deprecated and the in-tree scaleIO type is no longer supported.", alias="scaleIO")
    secret: Optional[IoK8sApiCoreV1SecretVolumeSource] = Field(default=None, description="secret represents a secret that should populate this volume. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret")
    storageos: Optional[IoK8sApiCoreV1StorageOSVolumeSource] = Field(default=None, description="storageOS represents a StorageOS volume attached and mounted on Kubernetes nodes. Deprecated: StorageOS is deprecated and the in-tree storageos type is no longer supported.")
    vsphere_volume: Optional[IoK8sApiCoreV1VsphereVirtualDiskVolumeSource] = Field(default=None, description="vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine. Deprecated: VsphereVolume is deprecated. All operations for the in-tree vsphereVolume type are redirected to the csi.vsphere.vmware.com CSI driver.", alias="vsphereVolume")
    __properties: ClassVar[List[str]] = ["awsElasticBlockStore", "azureDisk", "azureFile", "cephfs", "cinder", "configMap", "csi", "downwardAPI", "emptyDir", "ephemeral", "fc", "flexVolume", "flocker", "gcePersistentDisk", "gitRepo", "glusterfs", "hostPath", "image", "iscsi", "nfs", "persistentVolumeClaim", "photonPersistentDisk", "portworxVolume", "projected", "quobyte", "rbd", "scaleIO", "secret", "storageos", "vsphereVolume"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1VolumeSource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of aws_elastic_block_store
        if self.aws_elastic_block_store:
            _dict['awsElasticBlockStore'] = self.aws_elastic_block_store.to_dict()
        # override the default output from pydantic by calling `to_dict()` of azure_disk
        if self.azure_disk:
            _dict['azureDisk'] = self.azure_disk.to_dict()
        # override the default output from pydantic by calling `to_dict()` of azure_file
        if self.azure_file:
            _dict['azureFile'] = self.azure_file.to_dict()
        # override the default output from pydantic by calling `to_dict()` of cephfs
        if self.cephfs:
            _dict['cephfs'] = self.cephfs.to_dict()
        # override the default output from pydantic by calling `to_dict()` of cinder
        if self.cinder:
            _dict['cinder'] = self.cinder.to_dict()
        # override the default output from pydantic by calling `to_dict()` of config_map
        if self.config_map:
            _dict['configMap'] = self.config_map.to_dict()
        # override the default output from pydantic by calling `to_dict()` of csi
        if self.csi:
            _dict['csi'] = self.csi.to_dict()
        # override the default output from pydantic by calling `to_dict()` of downward_api
        if self.downward_api:
            _dict['downwardAPI'] = self.downward_api.to_dict()
        # override the default output from pydantic by calling `to_dict()` of empty_dir
        if self.empty_dir:
            _dict['emptyDir'] = self.empty_dir.to_dict()
        # override the default output from pydantic by calling `to_dict()` of ephemeral
        if self.ephemeral:
            _dict['ephemeral'] = self.ephemeral.to_dict()
        # override the default output from pydantic by calling `to_dict()` of fc
        if self.fc:
            _dict['fc'] = self.fc.to_dict()
        # override the default output from pydantic by calling `to_dict()` of flex_volume
        if self.flex_volume:
            _dict['flexVolume'] = self.flex_volume.to_dict()
        # override the default output from pydantic by calling `to_dict()` of flocker
        if self.flocker:
            _dict['flocker'] = self.flocker.to_dict()
        # override the default output from pydantic by calling `to_dict()` of gce_persistent_disk
        if self.gce_persistent_disk:
            _dict['gcePersistentDisk'] = self.gce_persistent_disk.to_dict()
        # override the default output from pydantic by calling `to_dict()` of git_repo
        if self.git_repo:
            _dict['gitRepo'] = self.git_repo.to_dict()
        # override the default output from pydantic by calling `to_dict()` of glusterfs
        if self.glusterfs:
            _dict['glusterfs'] = self.glusterfs.to_dict()
        # override the default output from pydantic by calling `to_dict()` of host_path
        if self.host_path:
            _dict['hostPath'] = self.host_path.to_dict()
        # override the default output from pydantic by calling `to_dict()` of image
        if self.image:
            _dict['image'] = self.image.to_dict()
        # override the default output from pydantic by calling `to_dict()` of iscsi
        if self.iscsi:
            _dict['iscsi'] = self.iscsi.to_dict()
        # override the default output from pydantic by calling `to_dict()` of nfs
        if self.nfs:
            _dict['nfs'] = self.nfs.to_dict()
        # override the default output from pydantic by calling `to_dict()` of persistent_volume_claim
        if self.persistent_volume_claim:
            _dict['persistentVolumeClaim'] = self.persistent_volume_claim.to_dict()
        # override the default output from pydantic by calling `to_dict()` of photon_persistent_disk
        if self.photon_persistent_disk:
            _dict['photonPersistentDisk'] = self.photon_persistent_disk.to_dict()
        # override the default output from pydantic by calling `to_dict()` of portworx_volume
        if self.portworx_volume:
            _dict['portworxVolume'] = self.portworx_volume.to_dict()
        # override the default output from pydantic by calling `to_dict()` of projected
        if self.projected:
            _dict['projected'] = self.projected.to_dict()
        # override the default output from pydantic by calling `to_dict()` of quobyte
        if self.quobyte:
            _dict['quobyte'] = self.quobyte.to_dict()
        # override the default output from pydantic by calling `to_dict()` of rbd
        if self.rbd:
            _dict['rbd'] = self.rbd.to_dict()
        # override the default output from pydantic by calling `to_dict()` of scale_io
        if self.scale_io:
            _dict['scaleIO'] = self.scale_io.to_dict()
        # override the default output from pydantic by calling `to_dict()` of secret
        if self.secret:
            _dict['secret'] = self.secret.to_dict()
        # override the default output from pydantic by calling `to_dict()` of storageos
        if self.storageos:
            _dict['storageos'] = self.storageos.to_dict()
        # override the default output from pydantic by calling `to_dict()` of vsphere_volume
        if self.vsphere_volume:
            _dict['vsphereVolume'] = self.vsphere_volume.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1VolumeSource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "awsElasticBlockStore": IoK8sApiCoreV1AWSElasticBlockStoreVolumeSource.from_dict(obj["awsElasticBlockStore"]) if obj.get("awsElasticBlockStore") is not None else None,
            "azureDisk": IoK8sApiCoreV1AzureDiskVolumeSource.from_dict(obj["azureDisk"]) if obj.get("azureDisk") is not None else None,
            "azureFile": IoK8sApiCoreV1AzureFileVolumeSource.from_dict(obj["azureFile"]) if obj.get("azureFile") is not None else None,
            "cephfs": IoK8sApiCoreV1CephFSVolumeSource.from_dict(obj["cephfs"]) if obj.get("cephfs") is not None else None,
            "cinder": IoK8sApiCoreV1CinderVolumeSource.from_dict(obj["cinder"]) if obj.get("cinder") is not None else None,
            "configMap": IoK8sApiCoreV1ConfigMapVolumeSource.from_dict(obj["configMap"]) if obj.get("configMap") is not None else None,
            "csi": IoK8sApiCoreV1CSIVolumeSource.from_dict(obj["csi"]) if obj.get("csi") is not None else None,
            "downwardAPI": IoK8sApiCoreV1DownwardAPIVolumeSource.from_dict(obj["downwardAPI"]) if obj.get("downwardAPI") is not None else None,
            "emptyDir": IoK8sApiCoreV1EmptyDirVolumeSource.from_dict(obj["emptyDir"]) if obj.get("emptyDir") is not None else None,
            "ephemeral": IoK8sApiCoreV1EphemeralVolumeSource.from_dict(obj["ephemeral"]) if obj.get("ephemeral") is not None else None,
            "fc": IoK8sApiCoreV1FCVolumeSource.from_dict(obj["fc"]) if obj.get("fc") is not None else None,
            "flexVolume": IoK8sApiCoreV1FlexVolumeSource.from_dict(obj["flexVolume"]) if obj.get("flexVolume") is not None else None,
            "flocker": IoK8sApiCoreV1FlockerVolumeSource.from_dict(obj["flocker"]) if obj.get("flocker") is not None else None,
            "gcePersistentDisk": IoK8sApiCoreV1GCEPersistentDiskVolumeSource.from_dict(obj["gcePersistentDisk"]) if obj.get("gcePersistentDisk") is not None else None,
            "gitRepo": IoK8sApiCoreV1GitRepoVolumeSource.from_dict(obj["gitRepo"]) if obj.get("gitRepo") is not None else None,
            "glusterfs": IoK8sApiCoreV1GlusterfsVolumeSource.from_dict(obj["glusterfs"]) if obj.get("glusterfs") is not None else None,
            "hostPath": IoK8sApiCoreV1HostPathVolumeSource.from_dict(obj["hostPath"]) if obj.get("hostPath") is not None else None,
            "image": IoK8sApiCoreV1ImageVolumeSource.from_dict(obj["image"]) if obj.get("image") is not None else None,
            "iscsi": IoK8sApiCoreV1ISCSIVolumeSource.from_dict(obj["iscsi"]) if obj.get("iscsi") is not None else None,
            "nfs": IoK8sApiCoreV1NFSVolumeSource.from_dict(obj["nfs"]) if obj.get("nfs") is not None else None,
            "persistentVolumeClaim": IoK8sApiCoreV1PersistentVolumeClaimVolumeSource.from_dict(obj["persistentVolumeClaim"]) if obj.get("persistentVolumeClaim") is not None else None,
            "photonPersistentDisk": IoK8sApiCoreV1PhotonPersistentDiskVolumeSource.from_dict(obj["photonPersistentDisk"]) if obj.get("photonPersistentDisk") is not None else None,
            "portworxVolume": IoK8sApiCoreV1PortworxVolumeSource.from_dict(obj["portworxVolume"]) if obj.get("portworxVolume") is not None else None,
            "projected": IoK8sApiCoreV1ProjectedVolumeSource.from_dict(obj["projected"]) if obj.get("projected") is not None else None,
            "quobyte": IoK8sApiCoreV1QuobyteVolumeSource.from_dict(obj["quobyte"]) if obj.get("quobyte") is not None else None,
            "rbd": IoK8sApiCoreV1RBDVolumeSource.from_dict(obj["rbd"]) if obj.get("rbd") is not None else None,
            "scaleIO": IoK8sApiCoreV1ScaleIOVolumeSource.from_dict(obj["scaleIO"]) if obj.get("scaleIO") is not None else None,
            "secret": IoK8sApiCoreV1SecretVolumeSource.from_dict(obj["secret"]) if obj.get("secret") is not None else None,
            "storageos": IoK8sApiCoreV1StorageOSVolumeSource.from_dict(obj["storageos"]) if obj.get("storageos") is not None else None,
            "vsphereVolume": IoK8sApiCoreV1VsphereVirtualDiskVolumeSource.from_dict(obj["vsphereVolume"]) if obj.get("vsphereVolume") is not None else None
        })
        return _obj


