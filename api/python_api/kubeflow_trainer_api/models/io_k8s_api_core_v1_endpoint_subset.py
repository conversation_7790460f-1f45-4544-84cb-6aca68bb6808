# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_endpoint_address import IoK8sApiCoreV1EndpointAddress
from kubeflow_trainer_api.models.io_k8s_api_core_v1_endpoint_port import IoK8sApiCoreV1EndpointPort
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1EndpointSubset(BaseModel):
    """
    EndpointSubset is a group of addresses with a common set of ports. The expanded set of endpoints is the Cartesian product of Addresses x Ports. For example, given:   {    Addresses: [{\"ip\": \"*********\"}, {\"ip\": \"*********\"}],    Ports:     [{\"name\": \"a\", \"port\": 8675}, {\"name\": \"b\", \"port\": 309}]  }  The resulting set of endpoints can be viewed as:   a: [ *********:8675, *********:8675 ],  b: [ *********:309, *********:309 ]
    """ # noqa: E501
    addresses: Optional[List[IoK8sApiCoreV1EndpointAddress]] = Field(default=None, description="IP addresses which offer the related ports that are marked as ready. These endpoints should be considered safe for load balancers and clients to utilize.")
    not_ready_addresses: Optional[List[IoK8sApiCoreV1EndpointAddress]] = Field(default=None, description="IP addresses which offer the related ports but are not currently marked as ready because they have not yet finished starting, have recently failed a readiness check, or have recently failed a liveness check.", alias="notReadyAddresses")
    ports: Optional[List[IoK8sApiCoreV1EndpointPort]] = Field(default=None, description="Port numbers available on the related IP addresses.")
    __properties: ClassVar[List[str]] = ["addresses", "notReadyAddresses", "ports"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1EndpointSubset from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in addresses (list)
        _items = []
        if self.addresses:
            for _item_addresses in self.addresses:
                if _item_addresses:
                    _items.append(_item_addresses.to_dict())
            _dict['addresses'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in not_ready_addresses (list)
        _items = []
        if self.not_ready_addresses:
            for _item_not_ready_addresses in self.not_ready_addresses:
                if _item_not_ready_addresses:
                    _items.append(_item_not_ready_addresses.to_dict())
            _dict['notReadyAddresses'] = _items
        # override the default output from pydantic by calling `to_dict()` of each item in ports (list)
        _items = []
        if self.ports:
            for _item_ports in self.ports:
                if _item_ports:
                    _items.append(_item_ports.to_dict())
            _dict['ports'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1EndpointSubset from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "addresses": [IoK8sApiCoreV1EndpointAddress.from_dict(_item) for _item in obj["addresses"]] if obj.get("addresses") is not None else None,
            "notReadyAddresses": [IoK8sApiCoreV1EndpointAddress.from_dict(_item) for _item in obj["notReadyAddresses"]] if obj.get("notReadyAddresses") is not None else None,
            "ports": [IoK8sApiCoreV1EndpointPort.from_dict(_item) for _item in obj["ports"]] if obj.get("ports") is not None else None
        })
        return _obj


