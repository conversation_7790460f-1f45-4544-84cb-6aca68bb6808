# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field, StrictInt, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1ContainerStateTerminated(BaseModel):
    """
    ContainerStateTerminated is a terminated state of a container.
    """ # noqa: E501
    container_id: Optional[StrictStr] = Field(default=None, description="Container's ID in the format '<type>://<container_id>'", alias="containerID")
    exit_code: StrictInt = Field(description="Exit status from the last termination of the container", alias="exitCode")
    finished_at: Optional[datetime] = Field(default=None, description="Time at which the container last terminated", alias="finishedAt")
    message: Optional[StrictStr] = Field(default=None, description="Message regarding the last termination of the container")
    reason: Optional[StrictStr] = Field(default=None, description="(brief) reason from the last termination of the container")
    signal: Optional[StrictInt] = Field(default=None, description="Signal from the last termination of the container")
    started_at: Optional[datetime] = Field(default=None, description="Time at which previous execution of the container started", alias="startedAt")
    __properties: ClassVar[List[str]] = ["containerID", "exitCode", "finishedAt", "message", "reason", "signal", "startedAt"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ContainerStateTerminated from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ContainerStateTerminated from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "containerID": obj.get("containerID"),
            "exitCode": obj.get("exitCode") if obj.get("exitCode") is not None else 0,
            "finishedAt": obj.get("finishedAt"),
            "message": obj.get("message"),
            "reason": obj.get("reason"),
            "signal": obj.get("signal"),
            "startedAt": obj.get("startedAt")
        })
        return _obj


