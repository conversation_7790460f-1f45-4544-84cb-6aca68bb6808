# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictBool, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.trainer_v1alpha1_initializer import TrainerV1alpha1Initializer
from kubeflow_trainer_api.models.trainer_v1alpha1_pod_spec_override import TrainerV1alpha1PodSpecOverride
from kubeflow_trainer_api.models.trainer_v1alpha1_runtime_ref import TrainerV1alpha1RuntimeRef
from kubeflow_trainer_api.models.trainer_v1alpha1_trainer import TrainerV1alpha1Trainer
from typing import Optional, Set
from typing_extensions import Self

class TrainerV1alpha1TrainJobSpec(BaseModel):
    """
    TrainJobSpec represents specification of the desired TrainJob.
    """ # noqa: E501
    annotations: Optional[Dict[str, StrictStr]] = Field(default=None, description="Annotations to apply for the derivative JobSet and Jobs. They will be merged with the TrainingRuntime values.")
    initializer: Optional[TrainerV1alpha1Initializer] = Field(default=None, description="Configuration of the initializer.")
    labels: Optional[Dict[str, StrictStr]] = Field(default=None, description="Labels to apply for the derivative JobSet and Jobs. They will be merged with the TrainingRuntime values.")
    managed_by: Optional[StrictStr] = Field(default=None, description="ManagedBy is used to indicate the controller or entity that manages a TrainJob. The value must be either an empty, `trainer.kubeflow.org/trainjob-controller` or `kueue.x-k8s.io/multikueue`. The built-in TrainJob controller reconciles TrainJob which don't have this field at all or the field value is the reserved string `trainer.kubeflow.org/trainjob-controller`, but delegates reconciling TrainJobs with a 'kueue.x-k8s.io/multikueue' to the Kueue. The field is immutable. Defaults to `trainer.kubeflow.org/trainjob-controller`", alias="managedBy")
    pod_spec_overrides: Optional[List[TrainerV1alpha1PodSpecOverride]] = Field(default=None, description="Custom overrides for the training runtime.", alias="podSpecOverrides")
    runtime_ref: TrainerV1alpha1RuntimeRef = Field(description="Reference to the training runtime. The field is immutable.", alias="runtimeRef")
    suspend: Optional[StrictBool] = Field(default=None, description="Whether the controller should suspend the running TrainJob. Defaults to false.")
    trainer: Optional[TrainerV1alpha1Trainer] = Field(default=None, description="Configuration of the trainer.")
    __properties: ClassVar[List[str]] = ["annotations", "initializer", "labels", "managedBy", "podSpecOverrides", "runtimeRef", "suspend", "trainer"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of TrainerV1alpha1TrainJobSpec from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of initializer
        if self.initializer:
            _dict['initializer'] = self.initializer.to_dict()
        # override the default output from pydantic by calling `to_dict()` of each item in pod_spec_overrides (list)
        _items = []
        if self.pod_spec_overrides:
            for _item_pod_spec_overrides in self.pod_spec_overrides:
                if _item_pod_spec_overrides:
                    _items.append(_item_pod_spec_overrides.to_dict())
            _dict['podSpecOverrides'] = _items
        # override the default output from pydantic by calling `to_dict()` of runtime_ref
        if self.runtime_ref:
            _dict['runtimeRef'] = self.runtime_ref.to_dict()
        # override the default output from pydantic by calling `to_dict()` of trainer
        if self.trainer:
            _dict['trainer'] = self.trainer.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of TrainerV1alpha1TrainJobSpec from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "annotations": obj.get("annotations"),
            "initializer": TrainerV1alpha1Initializer.from_dict(obj["initializer"]) if obj.get("initializer") is not None else None,
            "labels": obj.get("labels"),
            "managedBy": obj.get("managedBy"),
            "podSpecOverrides": [TrainerV1alpha1PodSpecOverride.from_dict(_item) for _item in obj["podSpecOverrides"]] if obj.get("podSpecOverrides") is not None else None,
            "runtimeRef": TrainerV1alpha1RuntimeRef.from_dict(obj["runtimeRef"]) if obj.get("runtimeRef") is not None else None,
            "suspend": obj.get("suspend"),
            "trainer": TrainerV1alpha1Trainer.from_dict(obj["trainer"]) if obj.get("trainer") is not None else None
        })
        return _obj


