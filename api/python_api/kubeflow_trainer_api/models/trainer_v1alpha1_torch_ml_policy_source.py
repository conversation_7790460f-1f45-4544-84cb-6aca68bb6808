# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_apimachinery_pkg_util_intstr_int_or_string import IoK8sApimachineryPkgUtilIntstrIntOrString
from kubeflow_trainer_api.models.trainer_v1alpha1_torch_elastic_policy import TrainerV1alpha1TorchElasticPolicy
from typing import Optional, Set
from typing_extensions import Self

class TrainerV1alpha1TorchMLPolicySource(BaseModel):
    """
    TorchMLPolicySource represents a PyTorch runtime configuration.
    """ # noqa: E501
    elastic_policy: Optional[TrainerV1alpha1TorchElasticPolicy] = Field(default=None, description="Elastic policy for the PyTorch training.", alias="elasticPolicy")
    num_proc_per_node: Optional[IoK8sApimachineryPkgUtilIntstrIntOrString] = Field(default=None, description="Number of processes per node. This value is inserted into the `--nproc-per-node` argument of the `torchrun` CLI. Supported values: `auto`, `cpu`, `gpu`, or int value. Defaults to `auto`.", alias="numProcPerNode")
    __properties: ClassVar[List[str]] = ["elasticPolicy", "numProcPerNode"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of TrainerV1alpha1TorchMLPolicySource from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of elastic_policy
        if self.elastic_policy:
            _dict['elasticPolicy'] = self.elastic_policy.to_dict()
        # override the default output from pydantic by calling `to_dict()` of num_proc_per_node
        if self.num_proc_per_node:
            _dict['numProcPerNode'] = self.num_proc_per_node.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of TrainerV1alpha1TorchMLPolicySource from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "elasticPolicy": TrainerV1alpha1TorchElasticPolicy.from_dict(obj["elasticPolicy"]) if obj.get("elasticPolicy") is not None else None,
            "numProcPerNode": IoK8sApimachineryPkgUtilIntstrIntOrString.from_dict(obj["numProcPerNode"]) if obj.get("numProcPerNode") is not None else None
        })
        return _obj


