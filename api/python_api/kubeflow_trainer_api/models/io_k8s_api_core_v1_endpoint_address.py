# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_object_reference import IoK8sApiCoreV1ObjectReference
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1EndpointAddress(BaseModel):
    """
    EndpointAddress is a tuple that describes single IP address.
    """ # noqa: E501
    hostname: Optional[StrictStr] = Field(default=None, description="The Hostname of this endpoint")
    ip: StrictStr = Field(description="The IP of this endpoint. May not be loopback (*********/8 or ::1), link-local (***********/16 or fe80::/10), or link-local multicast (*********/24 or ff02::/16).")
    node_name: Optional[StrictStr] = Field(default=None, description="Optional: Node hosting this endpoint. This can be used to determine endpoints local to a node.", alias="nodeName")
    target_ref: Optional[IoK8sApiCoreV1ObjectReference] = Field(default=None, description="Reference to object providing the endpoint.", alias="targetRef")
    __properties: ClassVar[List[str]] = ["hostname", "ip", "nodeName", "targetRef"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1EndpointAddress from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of target_ref
        if self.target_ref:
            _dict['targetRef'] = self.target_ref.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1EndpointAddress from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "hostname": obj.get("hostname"),
            "ip": obj.get("ip") if obj.get("ip") is not None else '',
            "nodeName": obj.get("nodeName"),
            "targetRef": IoK8sApiCoreV1ObjectReference.from_dict(obj["targetRef"]) if obj.get("targetRef") is not None else None
        })
        return _obj


