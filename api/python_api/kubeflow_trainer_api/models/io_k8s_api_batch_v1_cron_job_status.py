# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_object_reference import IoK8sApiCoreV1ObjectReference
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiBatchV1CronJobStatus(BaseModel):
    """
    CronJobStatus represents the current state of a cron job.
    """ # noqa: E501
    active: Optional[List[IoK8sApiCoreV1ObjectReference]] = Field(default=None, description="A list of pointers to currently running jobs.")
    last_schedule_time: Optional[datetime] = Field(default=None, description="Information when was the last time the job was successfully scheduled.", alias="lastScheduleTime")
    last_successful_time: Optional[datetime] = Field(default=None, description="Information when was the last time the job successfully completed.", alias="lastSuccessfulTime")
    __properties: ClassVar[List[str]] = ["active", "lastScheduleTime", "lastSuccessfulTime"]

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiBatchV1CronJobStatus from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each item in active (list)
        _items = []
        if self.active:
            for _item_active in self.active:
                if _item_active:
                    _items.append(_item_active.to_dict())
            _dict['active'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiBatchV1CronJobStatus from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "active": [IoK8sApiCoreV1ObjectReference.from_dict(_item) for _item in obj["active"]] if obj.get("active") is not None else None,
            "lastScheduleTime": obj.get("lastScheduleTime"),
            "lastSuccessfulTime": obj.get("lastSuccessfulTime")
        })
        return _obj


