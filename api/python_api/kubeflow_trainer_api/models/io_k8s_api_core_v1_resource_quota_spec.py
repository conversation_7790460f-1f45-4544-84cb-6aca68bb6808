# coding: utf-8

"""
    Kubeflow Trainer OpenAPI Spec

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

    The version of the OpenAPI document: unversioned
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""  # noqa: E501


from __future__ import annotations
import pprint
import re  # noqa: F401
import json

from pydantic import BaseModel, ConfigDict, Field, StrictStr, field_validator
from typing import Any, ClassVar, Dict, List, Optional
from kubeflow_trainer_api.models.io_k8s_api_core_v1_scope_selector import IoK8sApiCoreV1ScopeSelector
from kubeflow_trainer_api.models.io_k8s_apimachinery_pkg_api_resource_quantity import IoK8sApimachineryPkgApiResourceQuantity
from typing import Optional, Set
from typing_extensions import Self

class IoK8sApiCoreV1ResourceQuotaSpec(BaseModel):
    """
    ResourceQuotaSpec defines the desired hard limits to enforce for Quota.
    """ # noqa: E501
    hard: Optional[Dict[str, IoK8sApimachineryPkgApiResourceQuantity]] = Field(default=None, description="hard is the set of desired hard limits for each named resource. More info: https://kubernetes.io/docs/concepts/policy/resource-quotas/")
    scope_selector: Optional[IoK8sApiCoreV1ScopeSelector] = Field(default=None, description="scopeSelector is also a collection of filters like scopes that must match each object tracked by a quota but expressed using ScopeSelectorOperator in combination with possible values. For a resource to match, both scopes AND scopeSelector (if specified in spec), must be matched.", alias="scopeSelector")
    scopes: Optional[List[StrictStr]] = Field(default=None, description="A collection of filters that must match each object tracked by a quota. If not specified, the quota matches all objects.")
    __properties: ClassVar[List[str]] = ["hard", "scopeSelector", "scopes"]

    @field_validator('scopes')
    def scopes_validate_enum(cls, value):
        """Validates the enum"""
        if value is None:
            return value

        for i in value:
            if i not in set(['BestEffort', 'CrossNamespacePodAffinity', 'NotBestEffort', 'NotTerminating', 'PriorityClass', 'Terminating']):
                raise ValueError("each list item must be one of ('BestEffort', 'CrossNamespacePodAffinity', 'NotBestEffort', 'NotTerminating', 'PriorityClass', 'Terminating')")
        return value

    model_config = ConfigDict(
        populate_by_name=True,
        validate_assignment=True,
        protected_namespaces=(),
    )


    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.model_dump(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        # TODO: pydantic v2: use .model_dump_json(by_alias=True, exclude_unset=True) instead
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ResourceQuotaSpec from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self) -> Dict[str, Any]:
        """Return the dictionary representation of the model using alias.

        This has the following differences from calling pydantic's
        `self.model_dump(by_alias=True)`:

        * `None` is only added to the output dict for nullable fields that
          were set at model initialization. Other fields with value `None`
          are ignored.
        """
        excluded_fields: Set[str] = set([
        ])

        _dict = self.model_dump(
            by_alias=True,
            exclude=excluded_fields,
            exclude_none=True,
        )
        # override the default output from pydantic by calling `to_dict()` of each value in hard (dict)
        _field_dict = {}
        if self.hard:
            for _key_hard in self.hard:
                if self.hard[_key_hard]:
                    _field_dict[_key_hard] = self.hard[_key_hard].to_dict()
            _dict['hard'] = _field_dict
        # override the default output from pydantic by calling `to_dict()` of scope_selector
        if self.scope_selector:
            _dict['scopeSelector'] = self.scope_selector.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: Optional[Dict[str, Any]]) -> Optional[Self]:
        """Create an instance of IoK8sApiCoreV1ResourceQuotaSpec from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return cls.model_validate(obj)

        _obj = cls.model_validate({
            "hard": dict(
                (_k, IoK8sApimachineryPkgApiResourceQuantity.from_dict(_v))
                for _k, _v in obj["hard"].items()
            )
            if obj.get("hard") is not None
            else None,
            "scopeSelector": IoK8sApiCoreV1ScopeSelector.from_dict(obj["scopeSelector"]) if obj.get("scopeSelector") is not None else None,
            "scopes": obj.get("scopes")
        })
        return _obj


