// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"
	time "time"

	apistrainerv1alpha1 "github.com/kubeflow/trainer/pkg/apis/trainer/v1alpha1"
	versioned "github.com/kubeflow/trainer/pkg/client/clientset/versioned"
	internalinterfaces "github.com/kubeflow/trainer/pkg/client/informers/externalversions/internalinterfaces"
	trainerv1alpha1 "github.com/kubeflow/trainer/pkg/client/listers/trainer/v1alpha1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// ClusterTrainingRuntimeInformer provides access to a shared informer and lister for
// ClusterTrainingRuntimes.
type ClusterTrainingRuntimeInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() trainerv1alpha1.ClusterTrainingRuntimeLister
}

type clusterTrainingRuntimeInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// NewClusterTrainingRuntimeInformer constructs a new informer for ClusterTrainingRuntime type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewClusterTrainingRuntimeInformer(client versioned.Interface, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredClusterTrainingRuntimeInformer(client, resyncPeriod, indexers, nil)
}

// NewFilteredClusterTrainingRuntimeInformer constructs a new informer for ClusterTrainingRuntime type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredClusterTrainingRuntimeInformer(client versioned.Interface, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options v1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.TrainerV1alpha1().ClusterTrainingRuntimes().List(context.TODO(), options)
			},
			WatchFunc: func(options v1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.TrainerV1alpha1().ClusterTrainingRuntimes().Watch(context.TODO(), options)
			},
		},
		&apistrainerv1alpha1.ClusterTrainingRuntime{},
		resyncPeriod,
		indexers,
	)
}

func (f *clusterTrainingRuntimeInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredClusterTrainingRuntimeInformer(client, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *clusterTrainingRuntimeInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&apistrainerv1alpha1.ClusterTrainingRuntime{}, f.defaultInformer)
}

func (f *clusterTrainingRuntimeInformer) Lister() trainerv1alpha1.ClusterTrainingRuntimeLister {
	return trainerv1alpha1.NewClusterTrainingRuntimeLister(f.Informer().GetIndexer())
}
