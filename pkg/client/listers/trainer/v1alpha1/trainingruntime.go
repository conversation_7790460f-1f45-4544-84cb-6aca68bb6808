// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by lister-gen. DO NOT EDIT.

package v1alpha1

import (
	trainerv1alpha1 "github.com/kubeflow/trainer/pkg/apis/trainer/v1alpha1"
	labels "k8s.io/apimachinery/pkg/labels"
	listers "k8s.io/client-go/listers"
	cache "k8s.io/client-go/tools/cache"
)

// TrainingRuntimeLister helps list TrainingRuntimes.
// All objects returned here must be treated as read-only.
type TrainingRuntimeLister interface {
	// List lists all TrainingRuntimes in the indexer.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*trainerv1alpha1.TrainingRuntime, err error)
	// TrainingRuntimes returns an object that can list and get TrainingRuntimes.
	TrainingRuntimes(namespace string) TrainingRuntimeNamespaceLister
	TrainingRuntimeListerExpansion
}

// trainingRuntimeLister implements the TrainingRuntimeLister interface.
type trainingRuntimeLister struct {
	listers.ResourceIndexer[*trainerv1alpha1.TrainingRuntime]
}

// NewTrainingRuntimeLister returns a new TrainingRuntimeLister.
func NewTrainingRuntimeLister(indexer cache.Indexer) TrainingRuntimeLister {
	return &trainingRuntimeLister{listers.New[*trainerv1alpha1.TrainingRuntime](indexer, trainerv1alpha1.Resource("trainingruntime"))}
}

// TrainingRuntimes returns an object that can list and get TrainingRuntimes.
func (s *trainingRuntimeLister) TrainingRuntimes(namespace string) TrainingRuntimeNamespaceLister {
	return trainingRuntimeNamespaceLister{listers.NewNamespaced[*trainerv1alpha1.TrainingRuntime](s.ResourceIndexer, namespace)}
}

// TrainingRuntimeNamespaceLister helps list and get TrainingRuntimes.
// All objects returned here must be treated as read-only.
type TrainingRuntimeNamespaceLister interface {
	// List lists all TrainingRuntimes in the indexer for a given namespace.
	// Objects returned here must be treated as read-only.
	List(selector labels.Selector) (ret []*trainerv1alpha1.TrainingRuntime, err error)
	// Get retrieves the TrainingRuntime from the indexer for a given namespace and name.
	// Objects returned here must be treated as read-only.
	Get(name string) (*trainerv1alpha1.TrainingRuntime, error)
	TrainingRuntimeNamespaceListerExpansion
}

// trainingRuntimeNamespaceLister implements the TrainingRuntimeNamespaceLister
// interface.
type trainingRuntimeNamespaceLister struct {
	listers.ResourceIndexer[*trainerv1alpha1.TrainingRuntime]
}
