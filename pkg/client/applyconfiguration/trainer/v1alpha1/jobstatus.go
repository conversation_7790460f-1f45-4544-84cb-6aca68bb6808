// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// JobStatusApplyConfiguration represents a declarative configuration of the JobStatus type for use
// with apply.
type JobStatusApplyConfiguration struct {
	Name      *string `json:"name,omitempty"`
	Ready     *int32  `json:"ready,omitempty"`
	Succeeded *int32  `json:"succeeded,omitempty"`
	Failed    *int32  `json:"failed,omitempty"`
	Active    *int32  `json:"active,omitempty"`
	Suspended *int32  `json:"suspended,omitempty"`
}

// JobStatusApplyConfiguration constructs a declarative configuration of the JobStatus type for use with
// apply.
func JobStatus() *JobStatusApplyConfiguration {
	return &JobStatusApplyConfiguration{}
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithName(value string) *JobStatusApplyConfiguration {
	b.Name = &value
	return b
}

// WithReady sets the Ready field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Ready field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithReady(value int32) *JobStatusApplyConfiguration {
	b.Ready = &value
	return b
}

// WithSucceeded sets the Succeeded field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Succeeded field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithSucceeded(value int32) *JobStatusApplyConfiguration {
	b.Succeeded = &value
	return b
}

// WithFailed sets the Failed field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Failed field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithFailed(value int32) *JobStatusApplyConfiguration {
	b.Failed = &value
	return b
}

// WithActive sets the Active field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Active field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithActive(value int32) *JobStatusApplyConfiguration {
	b.Active = &value
	return b
}

// WithSuspended sets the Suspended field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Suspended field is set to the value of the last call.
func (b *JobStatusApplyConfiguration) WithSuspended(value int32) *JobStatusApplyConfiguration {
	b.Suspended = &value
	return b
}
