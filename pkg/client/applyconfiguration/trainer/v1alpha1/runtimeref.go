// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// RuntimeRefApplyConfiguration represents a declarative configuration of the RuntimeRef type for use
// with apply.
type RuntimeRefApplyConfiguration struct {
	Name     *string `json:"name,omitempty"`
	APIGroup *string `json:"apiGroup,omitempty"`
	Kind     *string `json:"kind,omitempty"`
}

// RuntimeRefApplyConfiguration constructs a declarative configuration of the RuntimeRef type for use with
// apply.
func RuntimeRef() *RuntimeRefApplyConfiguration {
	return &RuntimeRefApplyConfiguration{}
}

// WithName sets the Name field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Name field is set to the value of the last call.
func (b *RuntimeRefApplyConfiguration) WithName(value string) *RuntimeRefApplyConfiguration {
	b.Name = &value
	return b
}

// WithAPIGroup sets the APIGroup field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the APIGroup field is set to the value of the last call.
func (b *RuntimeRefApplyConfiguration) WithAPIGroup(value string) *RuntimeRefApplyConfiguration {
	b.APIGroup = &value
	return b
}

// WithKind sets the Kind field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Kind field is set to the value of the last call.
func (b *RuntimeRefApplyConfiguration) WithKind(value string) *RuntimeRefApplyConfiguration {
	b.Kind = &value
	return b
}
