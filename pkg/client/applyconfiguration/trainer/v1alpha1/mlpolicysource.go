// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// MLPolicySourceApplyConfiguration represents a declarative configuration of the MLPolicySource type for use
// with apply.
type MLPolicySourceApplyConfiguration struct {
	Torch *TorchMLPolicySourceApplyConfiguration `json:"torch,omitempty"`
	MPI   *MPIMLPolicySourceApplyConfiguration   `json:"mpi,omitempty"`
}

// MLPolicySourceApplyConfiguration constructs a declarative configuration of the MLPolicySource type for use with
// apply.
func MLPolicySource() *MLPolicySourceApplyConfiguration {
	return &MLPolicySourceApplyConfiguration{}
}

// WithTorch sets the Torch field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Torch field is set to the value of the last call.
func (b *MLPolicySourceApplyConfiguration) WithTorch(value *TorchMLPolicySourceApplyConfiguration) *MLPolicySourceApplyConfiguration {
	b.Torch = value
	return b
}

// WithMPI sets the MPI field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MPI field is set to the value of the last call.
func (b *MLPolicySourceApplyConfiguration) WithMPI(value *MPIMLPolicySourceApplyConfiguration) *MLPolicySourceApplyConfiguration {
	b.MPI = value
	return b
}
