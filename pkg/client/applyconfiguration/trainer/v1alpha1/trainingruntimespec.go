// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by applyconfiguration-gen. DO NOT EDIT.

package v1alpha1

// TrainingRuntimeSpecApplyConfiguration represents a declarative configuration of the TrainingRuntimeSpec type for use
// with apply.
type TrainingRuntimeSpecApplyConfiguration struct {
	MLPolicy       *MLPolicyApplyConfiguration           `json:"mlPolicy,omitempty"`
	PodGroupPolicy *PodGroupPolicyApplyConfiguration     `json:"podGroupPolicy,omitempty"`
	Template       *JobSetTemplateSpecApplyConfiguration `json:"template,omitempty"`
}

// TrainingRuntimeSpecApplyConfiguration constructs a declarative configuration of the TrainingRuntimeSpec type for use with
// apply.
func TrainingRuntimeSpec() *TrainingRuntimeSpecApplyConfiguration {
	return &TrainingRuntimeSpecApplyConfiguration{}
}

// WithMLPolicy sets the MLPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the MLPolicy field is set to the value of the last call.
func (b *TrainingRuntimeSpecApplyConfiguration) WithMLPolicy(value *MLPolicyApplyConfiguration) *TrainingRuntimeSpecApplyConfiguration {
	b.MLPolicy = value
	return b
}

// WithPodGroupPolicy sets the PodGroupPolicy field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the PodGroupPolicy field is set to the value of the last call.
func (b *TrainingRuntimeSpecApplyConfiguration) WithPodGroupPolicy(value *PodGroupPolicyApplyConfiguration) *TrainingRuntimeSpecApplyConfiguration {
	b.PodGroupPolicy = value
	return b
}

// WithTemplate sets the Template field in the declarative configuration to the given value
// and returns the receiver, so that objects can be built by chaining "With" function invocations.
// If called multiple times, the Template field is set to the value of the last call.
func (b *TrainingRuntimeSpecApplyConfiguration) WithTemplate(value *JobSetTemplateSpecApplyConfiguration) *TrainingRuntimeSpecApplyConfiguration {
	b.Template = value
	return b
}
