// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kubeflow/trainer/pkg/apis/trainer/v1alpha1"
	trainerv1alpha1 "github.com/kubeflow/trainer/pkg/client/applyconfiguration/trainer/v1alpha1"
	typedtrainerv1alpha1 "github.com/kubeflow/trainer/pkg/client/clientset/versioned/typed/trainer/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeClusterTrainingRuntimes implements ClusterTrainingRuntimeInterface
type fakeClusterTrainingRuntimes struct {
	*gentype.FakeClientWithListAndApply[*v1alpha1.ClusterTrainingRuntime, *v1alpha1.ClusterTrainingRuntimeList, *trainerv1alpha1.ClusterTrainingRuntimeApplyConfiguration]
	Fake *FakeTrainerV1alpha1
}

func newFakeClusterTrainingRuntimes(fake *FakeTrainerV1alpha1) typedtrainerv1alpha1.ClusterTrainingRuntimeInterface {
	return &fakeClusterTrainingRuntimes{
		gentype.NewFakeClientWithListAndApply[*v1alpha1.ClusterTrainingRuntime, *v1alpha1.ClusterTrainingRuntimeList, *trainerv1alpha1.ClusterTrainingRuntimeApplyConfiguration](
			fake.Fake,
			"",
			v1alpha1.SchemeGroupVersion.WithResource("clustertrainingruntimes"),
			v1alpha1.SchemeGroupVersion.WithKind("ClusterTrainingRuntime"),
			func() *v1alpha1.ClusterTrainingRuntime { return &v1alpha1.ClusterTrainingRuntime{} },
			func() *v1alpha1.ClusterTrainingRuntimeList { return &v1alpha1.ClusterTrainingRuntimeList{} },
			func(dst, src *v1alpha1.ClusterTrainingRuntimeList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.ClusterTrainingRuntimeList) []*v1alpha1.ClusterTrainingRuntime {
				return gentype.ToPointerSlice(list.Items)
			},
			func(list *v1alpha1.ClusterTrainingRuntimeList, items []*v1alpha1.ClusterTrainingRuntime) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
