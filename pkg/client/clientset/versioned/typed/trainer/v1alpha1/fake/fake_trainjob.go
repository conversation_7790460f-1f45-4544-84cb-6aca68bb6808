// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "github.com/kubeflow/trainer/pkg/apis/trainer/v1alpha1"
	trainerv1alpha1 "github.com/kubeflow/trainer/pkg/client/applyconfiguration/trainer/v1alpha1"
	typedtrainerv1alpha1 "github.com/kubeflow/trainer/pkg/client/clientset/versioned/typed/trainer/v1alpha1"
	gentype "k8s.io/client-go/gentype"
)

// fakeTrainJobs implements TrainJobInterface
type fakeTrainJobs struct {
	*gentype.FakeClientWithListAndApply[*v1alpha1.TrainJob, *v1alpha1.TrainJobList, *trainerv1alpha1.TrainJobApplyConfiguration]
	Fake *FakeTrainerV1alpha1
}

func newFakeTrainJobs(fake *FakeTrainerV1alpha1, namespace string) typedtrainerv1alpha1.TrainJobInterface {
	return &fakeTrainJobs{
		gentype.NewFakeClientWithListAndApply[*v1alpha1.TrainJob, *v1alpha1.TrainJobList, *trainerv1alpha1.TrainJobApplyConfiguration](
			fake.Fake,
			namespace,
			v1alpha1.SchemeGroupVersion.WithResource("trainjobs"),
			v1alpha1.SchemeGroupVersion.WithKind("TrainJob"),
			func() *v1alpha1.TrainJob { return &v1alpha1.TrainJob{} },
			func() *v1alpha1.TrainJobList { return &v1alpha1.TrainJobList{} },
			func(dst, src *v1alpha1.TrainJobList) { dst.ListMeta = src.ListMeta },
			func(list *v1alpha1.TrainJobList) []*v1alpha1.TrainJob { return gentype.ToPointerSlice(list.Items) },
			func(list *v1alpha1.TrainJobList, items []*v1alpha1.TrainJob) {
				list.Items = gentype.FromPointerSlice(items)
			},
		),
		fake,
	}
}
