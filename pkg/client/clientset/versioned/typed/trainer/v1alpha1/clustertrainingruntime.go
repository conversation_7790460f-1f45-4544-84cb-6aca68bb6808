// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	context "context"

	trainerv1alpha1 "github.com/kubeflow/trainer/pkg/apis/trainer/v1alpha1"
	applyconfigurationtrainerv1alpha1 "github.com/kubeflow/trainer/pkg/client/applyconfiguration/trainer/v1alpha1"
	scheme "github.com/kubeflow/trainer/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	gentype "k8s.io/client-go/gentype"
)

// ClusterTrainingRuntimesGetter has a method to return a ClusterTrainingRuntimeInterface.
// A group's client should implement this interface.
type ClusterTrainingRuntimesGetter interface {
	ClusterTrainingRuntimes() ClusterTrainingRuntimeInterface
}

// ClusterTrainingRuntimeInterface has methods to work with ClusterTrainingRuntime resources.
type ClusterTrainingRuntimeInterface interface {
	Create(ctx context.Context, clusterTrainingRuntime *trainerv1alpha1.ClusterTrainingRuntime, opts v1.CreateOptions) (*trainerv1alpha1.ClusterTrainingRuntime, error)
	Update(ctx context.Context, clusterTrainingRuntime *trainerv1alpha1.ClusterTrainingRuntime, opts v1.UpdateOptions) (*trainerv1alpha1.ClusterTrainingRuntime, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*trainerv1alpha1.ClusterTrainingRuntime, error)
	List(ctx context.Context, opts v1.ListOptions) (*trainerv1alpha1.ClusterTrainingRuntimeList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *trainerv1alpha1.ClusterTrainingRuntime, err error)
	Apply(ctx context.Context, clusterTrainingRuntime *applyconfigurationtrainerv1alpha1.ClusterTrainingRuntimeApplyConfiguration, opts v1.ApplyOptions) (result *trainerv1alpha1.ClusterTrainingRuntime, err error)
	ClusterTrainingRuntimeExpansion
}

// clusterTrainingRuntimes implements ClusterTrainingRuntimeInterface
type clusterTrainingRuntimes struct {
	*gentype.ClientWithListAndApply[*trainerv1alpha1.ClusterTrainingRuntime, *trainerv1alpha1.ClusterTrainingRuntimeList, *applyconfigurationtrainerv1alpha1.ClusterTrainingRuntimeApplyConfiguration]
}

// newClusterTrainingRuntimes returns a ClusterTrainingRuntimes
func newClusterTrainingRuntimes(c *TrainerV1alpha1Client) *clusterTrainingRuntimes {
	return &clusterTrainingRuntimes{
		gentype.NewClientWithListAndApply[*trainerv1alpha1.ClusterTrainingRuntime, *trainerv1alpha1.ClusterTrainingRuntimeList, *applyconfigurationtrainerv1alpha1.ClusterTrainingRuntimeApplyConfiguration](
			"clustertrainingruntimes",
			c.RESTClient(),
			scheme.ParameterCodec,
			"",
			func() *trainerv1alpha1.ClusterTrainingRuntime { return &trainerv1alpha1.ClusterTrainingRuntime{} },
			func() *trainerv1alpha1.ClusterTrainingRuntimeList {
				return &trainerv1alpha1.ClusterTrainingRuntimeList{}
			},
		),
	}
}
