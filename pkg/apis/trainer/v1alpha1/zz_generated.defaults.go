//go:build !ignore_autogenerated
// +build !ignore_autogenerated

// Copyright 2024 The Kubeflow Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by defaulter-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// RegisterDefaults adds defaulters functions to the given scheme.
// Public to allow building arbitrary schemes.
// All generated defaulters are covering - they call all nested defaulters.
func RegisterDefaults(scheme *runtime.Scheme) error {
	scheme.AddTypeDefaultingFunc(&ClusterTrainingRuntime{}, func(obj interface{}) { SetObjectDefaults_ClusterTrainingRuntime(obj.(*ClusterTrainingRuntime)) })
	scheme.AddTypeDefaultingFunc(&ClusterTrainingRuntimeList{}, func(obj interface{}) { SetObjectDefaults_ClusterTrainingRuntimeList(obj.(*ClusterTrainingRuntimeList)) })
	scheme.AddTypeDefaultingFunc(&TrainJob{}, func(obj interface{}) { SetObjectDefaults_TrainJob(obj.(*TrainJob)) })
	scheme.AddTypeDefaultingFunc(&TrainJobList{}, func(obj interface{}) { SetObjectDefaults_TrainJobList(obj.(*TrainJobList)) })
	scheme.AddTypeDefaultingFunc(&TrainingRuntime{}, func(obj interface{}) { SetObjectDefaults_TrainingRuntime(obj.(*TrainingRuntime)) })
	scheme.AddTypeDefaultingFunc(&TrainingRuntimeList{}, func(obj interface{}) { SetObjectDefaults_TrainingRuntimeList(obj.(*TrainingRuntimeList)) })
	return nil
}

func SetObjectDefaults_ClusterTrainingRuntime(in *ClusterTrainingRuntime) {
	for i := range in.Spec.Template.Spec.ReplicatedJobs {
		a := &in.Spec.Template.Spec.ReplicatedJobs[i]
		for j := range a.Template.Spec.Template.Spec.Volumes {
			b := &a.Template.Spec.Template.Spec.Volumes[j]
			if b.VolumeSource.ISCSI != nil {
				if b.VolumeSource.ISCSI.ISCSIInterface == "" {
					b.VolumeSource.ISCSI.ISCSIInterface = "default"
				}
			}
			if b.VolumeSource.RBD != nil {
				if b.VolumeSource.RBD.RBDPool == "" {
					b.VolumeSource.RBD.RBDPool = "rbd"
				}
				if b.VolumeSource.RBD.RadosUser == "" {
					b.VolumeSource.RBD.RadosUser = "admin"
				}
				if b.VolumeSource.RBD.Keyring == "" {
					b.VolumeSource.RBD.Keyring = "/etc/ceph/keyring"
				}
			}
			if b.VolumeSource.AzureDisk != nil {
				if b.VolumeSource.AzureDisk.CachingMode == nil {
					ptrVar1 := v1.AzureDataDiskCachingMode(v1.AzureDataDiskCachingReadWrite)
					b.VolumeSource.AzureDisk.CachingMode = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.FSType == nil {
					var ptrVar1 string = "ext4"
					b.VolumeSource.AzureDisk.FSType = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.ReadOnly == nil {
					var ptrVar1 bool = false
					b.VolumeSource.AzureDisk.ReadOnly = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.Kind == nil {
					ptrVar1 := v1.AzureDataDiskKind(v1.AzureSharedBlobDisk)
					b.VolumeSource.AzureDisk.Kind = &ptrVar1
				}
			}
			if b.VolumeSource.ScaleIO != nil {
				if b.VolumeSource.ScaleIO.StorageMode == "" {
					b.VolumeSource.ScaleIO.StorageMode = "ThinProvisioned"
				}
				if b.VolumeSource.ScaleIO.FSType == "" {
					b.VolumeSource.ScaleIO.FSType = "xfs"
				}
			}
		}
		for j := range a.Template.Spec.Template.Spec.InitContainers {
			b := &a.Template.Spec.Template.Spec.InitContainers[j]
			for k := range b.Ports {
				c := &b.Ports[k]
				if c.Protocol == "" {
					c.Protocol = "TCP"
				}
			}
			if b.LivenessProbe != nil {
				if b.LivenessProbe.ProbeHandler.GRPC != nil {
					if b.LivenessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.LivenessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.ReadinessProbe != nil {
				if b.ReadinessProbe.ProbeHandler.GRPC != nil {
					if b.ReadinessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.ReadinessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.StartupProbe != nil {
				if b.StartupProbe.ProbeHandler.GRPC != nil {
					if b.StartupProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.StartupProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
		}
		for j := range a.Template.Spec.Template.Spec.Containers {
			b := &a.Template.Spec.Template.Spec.Containers[j]
			for k := range b.Ports {
				c := &b.Ports[k]
				if c.Protocol == "" {
					c.Protocol = "TCP"
				}
			}
			if b.LivenessProbe != nil {
				if b.LivenessProbe.ProbeHandler.GRPC != nil {
					if b.LivenessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.LivenessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.ReadinessProbe != nil {
				if b.ReadinessProbe.ProbeHandler.GRPC != nil {
					if b.ReadinessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.ReadinessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.StartupProbe != nil {
				if b.StartupProbe.ProbeHandler.GRPC != nil {
					if b.StartupProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.StartupProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
		}
		for j := range a.Template.Spec.Template.Spec.EphemeralContainers {
			b := &a.Template.Spec.Template.Spec.EphemeralContainers[j]
			for k := range b.EphemeralContainerCommon.Ports {
				c := &b.EphemeralContainerCommon.Ports[k]
				if c.Protocol == "" {
					c.Protocol = "TCP"
				}
			}
			if b.EphemeralContainerCommon.LivenessProbe != nil {
				if b.EphemeralContainerCommon.LivenessProbe.ProbeHandler.GRPC != nil {
					if b.EphemeralContainerCommon.LivenessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.EphemeralContainerCommon.LivenessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.EphemeralContainerCommon.ReadinessProbe != nil {
				if b.EphemeralContainerCommon.ReadinessProbe.ProbeHandler.GRPC != nil {
					if b.EphemeralContainerCommon.ReadinessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.EphemeralContainerCommon.ReadinessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.EphemeralContainerCommon.StartupProbe != nil {
				if b.EphemeralContainerCommon.StartupProbe.ProbeHandler.GRPC != nil {
					if b.EphemeralContainerCommon.StartupProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.EphemeralContainerCommon.StartupProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
		}
	}
}

func SetObjectDefaults_ClusterTrainingRuntimeList(in *ClusterTrainingRuntimeList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_ClusterTrainingRuntime(a)
	}
}

func SetObjectDefaults_TrainJob(in *TrainJob) {
	for i := range in.Spec.PodSpecOverrides {
		a := &in.Spec.PodSpecOverrides[i]
		for j := range a.Volumes {
			b := &a.Volumes[j]
			if b.VolumeSource.ISCSI != nil {
				if b.VolumeSource.ISCSI.ISCSIInterface == "" {
					b.VolumeSource.ISCSI.ISCSIInterface = "default"
				}
			}
			if b.VolumeSource.RBD != nil {
				if b.VolumeSource.RBD.RBDPool == "" {
					b.VolumeSource.RBD.RBDPool = "rbd"
				}
				if b.VolumeSource.RBD.RadosUser == "" {
					b.VolumeSource.RBD.RadosUser = "admin"
				}
				if b.VolumeSource.RBD.Keyring == "" {
					b.VolumeSource.RBD.Keyring = "/etc/ceph/keyring"
				}
			}
			if b.VolumeSource.AzureDisk != nil {
				if b.VolumeSource.AzureDisk.CachingMode == nil {
					ptrVar1 := v1.AzureDataDiskCachingMode(v1.AzureDataDiskCachingReadWrite)
					b.VolumeSource.AzureDisk.CachingMode = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.FSType == nil {
					var ptrVar1 string = "ext4"
					b.VolumeSource.AzureDisk.FSType = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.ReadOnly == nil {
					var ptrVar1 bool = false
					b.VolumeSource.AzureDisk.ReadOnly = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.Kind == nil {
					ptrVar1 := v1.AzureDataDiskKind(v1.AzureSharedBlobDisk)
					b.VolumeSource.AzureDisk.Kind = &ptrVar1
				}
			}
			if b.VolumeSource.ScaleIO != nil {
				if b.VolumeSource.ScaleIO.StorageMode == "" {
					b.VolumeSource.ScaleIO.StorageMode = "ThinProvisioned"
				}
				if b.VolumeSource.ScaleIO.FSType == "" {
					b.VolumeSource.ScaleIO.FSType = "xfs"
				}
			}
		}
	}
}

func SetObjectDefaults_TrainJobList(in *TrainJobList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_TrainJob(a)
	}
}

func SetObjectDefaults_TrainingRuntime(in *TrainingRuntime) {
	for i := range in.Spec.Template.Spec.ReplicatedJobs {
		a := &in.Spec.Template.Spec.ReplicatedJobs[i]
		for j := range a.Template.Spec.Template.Spec.Volumes {
			b := &a.Template.Spec.Template.Spec.Volumes[j]
			if b.VolumeSource.ISCSI != nil {
				if b.VolumeSource.ISCSI.ISCSIInterface == "" {
					b.VolumeSource.ISCSI.ISCSIInterface = "default"
				}
			}
			if b.VolumeSource.RBD != nil {
				if b.VolumeSource.RBD.RBDPool == "" {
					b.VolumeSource.RBD.RBDPool = "rbd"
				}
				if b.VolumeSource.RBD.RadosUser == "" {
					b.VolumeSource.RBD.RadosUser = "admin"
				}
				if b.VolumeSource.RBD.Keyring == "" {
					b.VolumeSource.RBD.Keyring = "/etc/ceph/keyring"
				}
			}
			if b.VolumeSource.AzureDisk != nil {
				if b.VolumeSource.AzureDisk.CachingMode == nil {
					ptrVar1 := v1.AzureDataDiskCachingMode(v1.AzureDataDiskCachingReadWrite)
					b.VolumeSource.AzureDisk.CachingMode = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.FSType == nil {
					var ptrVar1 string = "ext4"
					b.VolumeSource.AzureDisk.FSType = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.ReadOnly == nil {
					var ptrVar1 bool = false
					b.VolumeSource.AzureDisk.ReadOnly = &ptrVar1
				}
				if b.VolumeSource.AzureDisk.Kind == nil {
					ptrVar1 := v1.AzureDataDiskKind(v1.AzureSharedBlobDisk)
					b.VolumeSource.AzureDisk.Kind = &ptrVar1
				}
			}
			if b.VolumeSource.ScaleIO != nil {
				if b.VolumeSource.ScaleIO.StorageMode == "" {
					b.VolumeSource.ScaleIO.StorageMode = "ThinProvisioned"
				}
				if b.VolumeSource.ScaleIO.FSType == "" {
					b.VolumeSource.ScaleIO.FSType = "xfs"
				}
			}
		}
		for j := range a.Template.Spec.Template.Spec.InitContainers {
			b := &a.Template.Spec.Template.Spec.InitContainers[j]
			for k := range b.Ports {
				c := &b.Ports[k]
				if c.Protocol == "" {
					c.Protocol = "TCP"
				}
			}
			if b.LivenessProbe != nil {
				if b.LivenessProbe.ProbeHandler.GRPC != nil {
					if b.LivenessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.LivenessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.ReadinessProbe != nil {
				if b.ReadinessProbe.ProbeHandler.GRPC != nil {
					if b.ReadinessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.ReadinessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.StartupProbe != nil {
				if b.StartupProbe.ProbeHandler.GRPC != nil {
					if b.StartupProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.StartupProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
		}
		for j := range a.Template.Spec.Template.Spec.Containers {
			b := &a.Template.Spec.Template.Spec.Containers[j]
			for k := range b.Ports {
				c := &b.Ports[k]
				if c.Protocol == "" {
					c.Protocol = "TCP"
				}
			}
			if b.LivenessProbe != nil {
				if b.LivenessProbe.ProbeHandler.GRPC != nil {
					if b.LivenessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.LivenessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.ReadinessProbe != nil {
				if b.ReadinessProbe.ProbeHandler.GRPC != nil {
					if b.ReadinessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.ReadinessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.StartupProbe != nil {
				if b.StartupProbe.ProbeHandler.GRPC != nil {
					if b.StartupProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.StartupProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
		}
		for j := range a.Template.Spec.Template.Spec.EphemeralContainers {
			b := &a.Template.Spec.Template.Spec.EphemeralContainers[j]
			for k := range b.EphemeralContainerCommon.Ports {
				c := &b.EphemeralContainerCommon.Ports[k]
				if c.Protocol == "" {
					c.Protocol = "TCP"
				}
			}
			if b.EphemeralContainerCommon.LivenessProbe != nil {
				if b.EphemeralContainerCommon.LivenessProbe.ProbeHandler.GRPC != nil {
					if b.EphemeralContainerCommon.LivenessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.EphemeralContainerCommon.LivenessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.EphemeralContainerCommon.ReadinessProbe != nil {
				if b.EphemeralContainerCommon.ReadinessProbe.ProbeHandler.GRPC != nil {
					if b.EphemeralContainerCommon.ReadinessProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.EphemeralContainerCommon.ReadinessProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
			if b.EphemeralContainerCommon.StartupProbe != nil {
				if b.EphemeralContainerCommon.StartupProbe.ProbeHandler.GRPC != nil {
					if b.EphemeralContainerCommon.StartupProbe.ProbeHandler.GRPC.Service == nil {
						var ptrVar1 string = ""
						b.EphemeralContainerCommon.StartupProbe.ProbeHandler.GRPC.Service = &ptrVar1
					}
				}
			}
		}
	}
}

func SetObjectDefaults_TrainingRuntimeList(in *TrainingRuntimeList) {
	for i := range in.Items {
		a := &in.Items[i]
		SetObjectDefaults_TrainingRuntime(a)
	}
}
