/*
Copyright 2025 The Kubeflow Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package constants

const (
	// VolumeNameInitializer is the name for the initializer Pod's Volume and VolumeMount.
	// TODO (andreyvelich): Add validation to check that initializer Pod has the correct volume.
	VolumeNameInitializer string = "initializer"

	// InitializerEnvStorageUri is the env name for the initializer storage uri.
	InitializerEnvStorageUri string = "STORAGE_URI"
)
