apiVersion: trainer.kubeflow.org/v1alpha1
kind: ClusterTrainingRuntime
metadata:
  name: sft-with-pvc
spec:
  mlPolicy:
    numNodes: 1
    torch:
      numProcPerNode: auto
  template:
    spec:
      replicatedJobs:
        # 数据集初始化器 Job
        - name: dataset-initializer
          template:
            metadata:
              labels:
                trainer.kubeflow.org/trainjob-ancestor-step: dataset-initializer
            spec:
              template:
                spec:
                  restartPolicy: Never
                  containers:
                    - name: dataset-initializer
                      image: hub.cetccloud.io:5000/jdcloud/kserve-storage-initializer:v2.0.0 
                      env:
                        - name: STORAGE_URI
                          value: ""  # 将由 TrainJob 覆盖
                        - name: CETCCLOUD_REPO_TYPE
                          value: dataset
                        - name: DOWNLOAD_PATH
                          value: "/workspace/data"
                      volumeMounts:
                        - name: shared-workspace
                          mountPath: /workspace
                      command:
                        - /bin/sh
                        - -c
                      args:
                        - |
                          echo "Starting dataset initialization..."
                          echo "Storage URI: $STORAGE_URI"
                          echo "Download path: $DOWNLOAD_PATH"
                          # 实际的下载逻辑会由 storage-initializer 镜像处理
                          # 这里会被 TrainJob controller 添加 --storage-uri 参数
                          echo "Dataset initialization completed"
                  volumes:
                    - name: shared-workspace
                      persistentVolumeClaim:
                        claimName: ""  # 将由 TrainJob 动态设置
        
        # 模型初始化器 Job
        - name: model-initializer
          template:
            metadata:
              labels:
                trainer.kubeflow.org/trainjob-ancestor-step: model-initializer
            spec:
              template:
                spec:
                  restartPolicy: Never
                  containers:
                    - name: model-initializer
                      image: hub.cetccloud.io:5000/jdcloud/kserve-storage-initializer:v2.0.0
                      env:
                        - name: STORAGE_URI
                          value: ""  # 将由 TrainJob 覆盖
                        - name: DOWNLOAD_PATH
                          value: "/workspace/model"
                      volumeMounts:
                        - name: shared-workspace
                          mountPath: /workspace
                      command:
                        - /bin/sh
                        - -c
                      args:
                        - |
                          echo "Starting model initialization..."
                          echo "Storage URI: $STORAGE_URI"
                          echo "Download path: $DOWNLOAD_PATH"
                          # 实际的下载逻辑会由 storage-initializer 镜像处理
                          # 这里会被 TrainJob controller 添加 --storage-uri 参数
                          echo "Model initialization completed"
                  volumes:
                    - name: shared-workspace
                      persistentVolumeClaim:
                        claimName: ""  # 将由 TrainJob 动态设置
        
        # 训练 Job
        - name: node
          template:
            metadata:
              labels:
                trainer.kubeflow.org/trainjob-ancestor-step: trainer
            spec:
              template:
                spec:
                  containers:
                    - name: node
                      image: pytorch/pytorch:2.7.1-cuda12.8-cudnn9-runtime
                      env:
                        - name: MODEL_PATH
                          value: "/workspace/model"
                        - name: DATA_PATH
                          value: "/workspace/data"
                        - name: OUTPUT_PATH
                          value: "/workspace/output"
                      volumeMounts:
                        - name: shared-workspace
                          mountPath: /workspace
                      command:
                        - /bin/bash
                        - -c
                      args:
                        - |
                          echo "Starting training..."
                          echo "Model path: $MODEL_PATH"
                          echo "Data path: $DATA_PATH"
                          echo "Output path: $OUTPUT_PATH"
                          ls -la /workspace/
                          # 训练命令将由 TrainJob 的 trainer.command 和 trainer.args 覆盖
                  volumes:
                    - name: shared-workspace
                      persistentVolumeClaim:
                        claimName: ""  # 将由 TrainJob 动态设置
