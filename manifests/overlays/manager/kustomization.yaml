apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

# Namespace where all resources are deployed.
namespace: kubeflow-system

resources:
  - namespace.yaml
  - ../../base/crds
  - ../../base/manager
  - ../../base/rbac
  - ../../base/webhook
  - ../../third-party/jobset # Comment this line if JobSet is installed on the Kubernetes cluster.

# Update the Kubeflow Trainer controller manager image tag.
images:
  - name: ghcr.io/kubeflow/trainer/trainer-controller-manager
    newTag: v2.0.0-rc.0

# Secret for the Kubeflow Training webhook.
secretGenerator:
  - name: kubeflow-trainer-webhook-cert
    namespace: kubeflow-system
    options:
      disableNameSuffixHash: true
