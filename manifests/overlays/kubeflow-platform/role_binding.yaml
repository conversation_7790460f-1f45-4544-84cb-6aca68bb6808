apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kubeflow-trainer-view
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kubeflow-trainer-view
subjects:
  - kind: ServiceAccount
    name: notebook-controller-service-account   # Notebook Service Account

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kubeflow-trainer-view
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kubeflow-trainer-view
subjects:
  - kind: ServiceAccount
    name: controller-service-account   # Profile Service Account
