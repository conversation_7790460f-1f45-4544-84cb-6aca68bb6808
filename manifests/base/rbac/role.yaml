---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kubeflow-trainer-controller-manager
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  - secrets
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - admissionregistration.k8s.io
  resources:
  - validatingwebhookconfigurations
  verbs:
  - get
  - list
  - update
  - watch
- apiGroups:
  - jobset.x-k8s.io
  resources:
  - jobsets
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - scheduling.x-k8s.io
  resources:
  - podgroups
  verbs:
  - create
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - trainer.kubeflow.org
  resources:
  - clustertrainingruntimes
  - trainingruntimes
  - trainjobs
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - trainer.kubeflow.org
  resources:
  - clustertrainingruntimes/finalizers
  - trainingruntimes/finalizers
  - trainjobs/finalizers
  - trainjobs/status
  verbs:
  - get
  - patch
  - update
