# TODO (and<PERSON><PERSON><PERSON>): Change this to DeepSpeed or MLX runtime.
apiVersion: trainer.kubeflow.org/v1alpha1
kind: ClusterTrainingRuntime
metadata:
  name: mpi-distributed
spec:
  mlPolicy:
    numNodes: 1
    mpi:
      numProcPerNode: 1
      mpiImplementation: OpenMPI
      sshAuthMountPath: /home/<USER>/.ssh
      runLauncherAsNode: true
  template:
    spec:
      network:
        publishNotReadyAddresses: true
      successPolicy:
        operator: All
        targetReplicatedJobs:
          - launcher
      replicatedJobs:
        - name: launcher
          template:
            metadata:
              labels:
                trainer.kubeflow.org/trainjob-ancestor-step: trainer
            spec:
              template:
                spec:
                  containers:
                  - name: node
                    image: mpioperator/mpi-pi:openmpi
                    securityContext:
                      runAsUser: 1000
                    command:
                      - mpirun
                    args:
                      - /home/<USER>/pi
        - name: node
          template:
            spec:
              template:
                spec:
                  containers:
                    - name: node
                      image: mpioperator/mpi-pi:openmpi
                      securityContext:
                        runAsUser: 1000
                      command:
                        - /usr/sbin/sshd
                      args:
                        - -De
                        - -f
                        - /home/<USER>/.sshd_config
                      readinessProbe:
                        tcpSocket:
                          port: 2222
                        initialDelaySeconds: 5
