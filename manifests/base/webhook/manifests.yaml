---
apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: validating-webhook-configuration
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-trainer-kubeflow-org-v1alpha1-clustertrainingruntime
  failurePolicy: Fail
  name: validator.clustertrainingruntime.trainer.kubeflow.org
  rules:
  - apiGroups:
    - trainer.kubeflow.org
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - clustertrainingruntimes
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-trainer-kubeflow-org-v1alpha1-trainingruntime
  failurePolicy: Fail
  name: validator.trainingruntime.trainer.kubeflow.org
  rules:
  - apiGroups:
    - trainer.kubeflow.org
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - trainingruntimes
  sideEffects: None
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: webhook-service
      namespace: system
      path: /validate-trainer-kubeflow-org-v1alpha1-trainjob
  failurePolicy: Fail
  name: validator.trainjob.trainer.kubeflow.org
  rules:
  - apiGroups:
    - trainer.kubeflow.org
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - trainjobs
  sideEffects: None
