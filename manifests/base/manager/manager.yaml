---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kubeflow-trainer-controller-manager
  labels:
    app.kubernetes.io/name: trainer
    app.kubernetes.io/component: manager
    app.kubernetes.io/part-of: kubeflow
spec:
  selector:
    matchLabels:
      app.kubernetes.io/name: trainer
      app.kubernetes.io/component: manager
      app.kubernetes.io/part-of: kubeflow
  template:
    metadata:
      labels:
        app.kubernetes.io/name: trainer
        app.kubernetes.io/component: manager
        app.kubernetes.io/part-of: kubeflow
    spec:
      containers:
        - name: manager
          image: ghcr.io/kubeflow/trainer/trainer-controller-manager
          volumeMounts:
            - mountPath: /tmp/k8s-webhook-server/serving-certs
              name: cert
              readOnly: true
          livenessProbe:
            httpGet:
              path: /healthz
              port: 8081
            initialDelaySeconds: 15
            periodSeconds: 20
            timeoutSeconds: 3
          readinessProbe:
            httpGet:
              path: /readyz
              port: 8081
            initialDelaySeconds: 10
            periodSeconds: 15
            timeoutSeconds: 3
      serviceAccountName: kubeflow-trainer-controller-manager
      volumes:
        - name: cert
          secret:
            defaultMode: 420
            secretName: kubeflow-trainer-webhook-cert
---
apiVersion: v1
kind: Service
metadata:
  name: kubeflow-trainer-controller-manager
spec:
  ports:
    - name: monitoring-port
      port: 8080
      targetPort: 8080
    - name: webhook-server
      port: 443
      protocol: TCP
      targetPort: 9443
  selector:
    app.kubernetes.io/component: manager
