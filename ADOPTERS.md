# Adopters of Kubeflow Trainer

This page contains a list of organizations who are using Kubeflow Trainer.
If you'd like to be included here, please send a pull request which modifies this file.
Please keep the list in alphabetical order.

| Organization | Contact |
| ------------ | ------- |
| [Akuity](https://akuity.io/) | [<PERSON>](https://github.com/terrytangyuan) |
| [Alibaba Cloud](https://us.alibabacloud.com/) | [<PERSON>](https://github.com/cheyang) |
| [Alibaba DAMO Academy](https://damo.alibaba.com/) | [<PERSON><PERSON>](https://damo.alibaba.com/about/) |
| [Amazon Web Services](https://aws.amazon.com/) | [Ji<PERSON><PERSON>](https://github.com/Jeffwan) |
| [Ant Group](https://www.antgroup.com/) | [<PERSON>](https://github.com/terrytangyuan) |
| [Atrio](https://www.atrio.io/) | [<PERSON>](https://github.com/cesargomez) |
| [Bloomberg](https://www.bloomberg.com/) | [<PERSON><PERSON><PERSON>](https://github.com/czheng94) and [Dan Sun](https://github.com/yuzisun) |
| [Bytedance](https://www.bytedance.com/) | [Jiaxin Shan](https://github.com/Jeffwan) |
| [Cisco](https://www.cisco.com/) | [Andrey Velichkevich](https://github.com/andreyvelich) and [Ramdoot Kumar](https://github.com/ramdootp) |
| [Huawei](https://www.huawei.com/) | [Lei Su](https://github.com/suleisl2000) and [Fei Xu](https://github.com/fisherxu) |
| [Iguazio](https://www.iguazio.com/) | [Yaron Haviv](https://github.com/yaronha) |
| [Mellanox Technologies](https://www.mellanox.com/) | [Vitaliy Razinkov](https://github.com/vtlrazin) |
| [NVIDIA](https://www.nvidia.com/) | [Rong Ou](https://github.com/rongou) |
| [Pinduoduo](https://en.pinduoduo.com/) | [Shuwen Wang](https://github.com/antshuwen) |
| [Polyaxon](https://polyaxon.com/) | [Mourad Mourafiq](https://github.com/mouradmourafiq) |
| [Qihoo 360](https://www.360.cn/) | [Xigang Wang](https://github.com/xigang) |
| [Qutoutiao](https://www.qutoutiao.net/) | [Zhaojing Yu](https://github.com/yuzhaojing) |
| [Tencent](http://tencent.com/en-us/) | [Ce Gao](https://github.com/gaocegege), [Wang Zhang](https://github.com/zw0610) and [Lei Xue](https://github.com/carmark)  |
| [TuSimple](https://www.tusimple.com/) | [Hengliang He](https://github.com/henglianghe) |
| [vip](https://www.vip.com/) | [Harold Miao](https://github.com/oikomi) |
