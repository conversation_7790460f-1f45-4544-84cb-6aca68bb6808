apiVersion: trainer.kubeflow.org/v1alpha1
kind: ClusterTrainingRuntime
metadata:
  name: sft-with-initcontainers
spec:
  mlPolicy:
    numNodes: 1
    torch:
      numProcPerNode: auto
  template:
    spec:
      replicatedJobs:
        # 训练 Job - 包含 initContainers
        - name: node
          template:
            metadata:
              labels:
                trainer.kubeflow.org/trainjob-ancestor-step: trainer
            spec:
              template:
                spec:
                  # Pod 重启策略
                  restartPolicy: Never

                  # Init Containers - 按顺序执行数据集和模型初始化
                  initContainers:
                    # 数据集初始化器 InitContainer
                    - name: dataset-initializer
                      image: hub.cetccloud.io:5000/jdcloud/kserve-storage-initializer:v2.0.0
                      env:
                        - name: STORAGE_URI
                          value: ""  # 将由 TrainJob 覆盖
                        - name: CETCCLOUD_REPO_TYPE
                          value: dataset
                      volumeMounts:
                        - name: workspace
                          mountPath: /workspace
                          subPath: data  # 数据集存储在 /workspace/data

                    # 模型初始化器 InitContainer
                    - name: model-initializer
                      image: hub.cetccloud.io:5000/jdcloud/kserve-storage-initializer:v2.0.0
                      env:
                        - name: STORAGE_URI
                          value: ""  # 将由 TrainJob 覆盖
                      volumeMounts:
                        - name: workspace
                          mountPath: /workspace
                          subPath: model  # 模型存储在 /workspace/model
                  
                  # 主训练容器
                  containers:
                    - name: node
                      image: pytorch/pytorch:2.7.1-cuda12.8-cudnn9-runtime
                      volumeMounts:
                        - name: workspace
                          mountPath: /workspace
                      env:
                        - name: MODEL_PATH
                          value: "/workspace/model"
                        - name: DATA_PATH
                          value: "/workspace/data"
                  
                  # 共享的 emptyDir 卷
                  volumes:
                    - name: workspace
                      emptyDir:
                        sizeLimit: 100Gi  # 可选：限制大小
