---
# 首先创建 PVC
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: llm-sft-workspace
  namespace: default
  labels:
    app: llm-sft-training
    trainjob: llm-sft-with-pvc
spec:
  accessModes:
    - ReadWriteMany  # 支持多个 Pod 同时访问
  resources:
    requests:
      storage: 500Gi  # 为模型、数据集和输出预留足够空间
  storageClassName: nfs-storage  # 根据您的集群配置调整

---
# TrainJob 配置
apiVersion: trainer.kubeflow.org/v1alpha1
kind: TrainJob
metadata:
  name: llm-sft-with-pvc
  namespace: default
spec:
  runtimeRef:
    name: sft-with-pvc
    apiGroup: trainer.kubeflow.org
    kind: ClusterTrainingRuntime
  
  # 初始化器配置
  initializer:
    # 数据集初始化 - 下载到 /workspace/data
    dataset:
      storageUri: "cc://root/alpaca-gpt4-data-zh"
      secretRef: 
        name: cetccloud-credentials
      env:
        - name: DOWNLOAD_PATH
          value: "/workspace/data"
        - name: CETCCLOUD_REPO_TYPE
          value: "dataset"
    
    # 模型初始化 - 下载到 /workspace/model
    model:
      storageUri: "cc://root/Qwen2.5-7B-Instruct"
      secretRef:
        name: cetccloud-credentials
      env:
        - name: DOWNLOAD_PATH
          value: "/workspace/model"
  
  # 训练器配置
  trainer:
    image: 172.28.0.32:3443/jdcloud/swift:ubuntu22.04-cuda12.4.0-py311-torch2.6.0-vllm0.8.5.post1-modelscope1.26.0-swift3.4.1.post1
    command:
      - swift
    args:
      - sft
      - --model_type=qwen2_5-7b-instruct
      - --model_id_or_path=/workspace/model
      - --dataset_dir=/workspace/data
      - --torch_dtype=bfloat16
      - --num_train_epochs=1
      - --per_device_train_batch_size=1
      - --per_device_eval_batch_size=1
      - --learning_rate=1e-4
      - --lora_rank=8
      - --lora_alpha=32
      - --target_modules=all-linear
      - --gradient_accumulation_steps=16
      - --eval_steps=50
      - --save_steps=50
      - --save_total_limit=2
      - --logging_steps=5
      - --max_length=2048
      - --output_dir=/workspace/output
      - --system=You are a helpful assistant.
      - --warmup_ratio=0.05
      - --dataloader_num_workers=4
      - --model_author=swift
      - --model_name=swift-robot
    numNodes: 2
    numProcPerNode: 2
    resourcesPerNode:
      requests:
        nvidia.com/gpu: 2
        cpu: 8
        memory: 32Gi
      limits:
        nvidia.com/gpu: 2
        cpu: 16
        memory: 64Gi
    env:
      - name: NCCL_DEBUG
        value: "INFO"
      - name: TORCH_DISTRIBUTED_DEBUG
        value: "DETAIL"
  
  # Pod 规格覆盖 - 为所有 Job 配置相同的 PVC
  podSpecOverrides:
    # 数据集初始化器 Job 的 PVC 配置
    - targetJobs:
        - name: dataset-initializer
      volumes:
        - name: shared-workspace
          persistentVolumeClaim:
            claimName: llm-sft-workspace
    
    # 模型初始化器 Job 的 PVC 配置
    - targetJobs:
        - name: model-initializer
      volumes:
        - name: shared-workspace
          persistentVolumeClaim:
            claimName: llm-sft-workspace
    
    # 训练 Job 的 PVC 配置
    - targetJobs:
        - name: node
      volumes:
        - name: shared-workspace
          persistentVolumeClaim:
            claimName: llm-sft-workspace
  
  labels:
    experiment: "llama-sft-pvc"
    version: "v1.0"
  annotations:
    description: "LLaMA SFT with shared PVC for model and dataset storage"
