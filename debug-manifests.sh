#!/bin/bash

set -e
set -x

echo "=== Debug make manifests ==="

echo "1. Checking Go version..."
go version

echo "2. Checking Go modules..."
go mod verify

echo "3. Checking controller-gen..."
if [ -f "./bin/controller-gen" ]; then
    echo "controller-gen exists"
    ./bin/controller-gen --version
else
    echo "Installing controller-gen..."
    make controller-gen
fi

echo "4. Checking directories..."
ls -la manifests/base/

echo "5. Testing Go build..."
go build ./pkg/apis/trainer/v1alpha1/...

echo "6. Running controller-gen with verbose output..."
./bin/controller-gen "crd:generateEmbeddedObjectMeta=true" rbac:roleName=kubeflow-trainer-controller-manager webhook \
    paths="./pkg/apis/trainer/v1alpha1/...;./pkg/controller/...;./pkg/runtime/...;./pkg/webhooks/...;./pkg/util/cert/..." \
    output:crd:artifacts:config=manifests/base/crds \
    output:rbac:artifacts:config=manifests/base/rbac \
    output:webhook:artifacts:config=manifests/base/webhook

echo "7. Checking generated files..."
ls -la manifests/base/crds/
ls -la manifests/base/rbac/
ls -la manifests/base/webhook/

echo "=== Debug completed ==="
